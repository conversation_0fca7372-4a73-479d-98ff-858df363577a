import { api } from '@/lib/api';
import {
  Event,
  Promoter,
  PriceCategory,
  ApiResponse,
  PaginatedResponse,
  PromotersPaginatedResponse,
  EventFilters,
  PromoterFilters,
  PortalSettingsResponse
} from '@/types';

// Get public events with filters
export const getPublicEvents = async (params: EventFilters = {}) => {
  const response = await api.get<ApiResponse<PaginatedResponse<Event>>>('/api/public/events', { params });
  return response.data;
};

// Get single public event
export const getPublicEvent = async (eventId: string) => {
  const response = await api.get<ApiResponse<Event>>(`/api/public/events/${eventId}`);
  return response.data;
};

// Get available price categories for an event
export const getAvailableCategories = async (eventId: string) => {
  const response = await api.get<ApiResponse<PriceCategory[]>>(`/api/public/events/${eventId}/categories/available`);
  return response.data;
};

// Get public promoters
export const getPublicPromoters = async (params: PromoterFilters = {}) => {
  const response = await api.get<ApiResponse<PromotersPaginatedResponse>>('/api/public/promoters', { params });
  return response.data;
};

// Get single public promoter
export const getPublicPromoter = async (promoterId: string) => {
  const response = await api.get<ApiResponse<Promoter>>(`/api/public/promoters/${promoterId}`);
  return response.data;
};

// Get promoter's public events
export const getPromoterPublicEvents = async (promoterId: string, params: EventFilters = {}) => {
  const response = await api.get<ApiResponse<PaginatedResponse<Event>>>(
    `/api/public/promoters/${promoterId}/events`,
    { params }
  );
  return response.data;
};

// Get portal settings for a promoter
export const getPortalSettings = async (promoterId: string) => {
  console.log('getPortalSettings called with promoterId:', promoterId);
  console.log('API base URL:', api.defaults.baseURL);

  try {
    const response = await api.get<PortalSettingsResponse>(`/api/portal-settings/view/${promoterId}`);
    console.log('Portal settings API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Portal settings API error:', error);
    throw error;
  }
};

// Purchase tickets (to be implemented)
export const purchaseTickets = async (ticketData: {
  eventId: string;
  categoryId: string;
  quantity: number;
  customerInfo: {
    name: string;
    email: string;
    phone?: string;
  };
}) => {
  const response = await api.post<ApiResponse<any>>('/tickets/purchase', ticketData);
  return response.data;
};

// Process payment
export const processPayment = async (paymentData: {
  eventId: string;
  selectedCategories: Array<{
    categoryId: string;
    quantity: number;
  }>;
  customerInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
  };
  paymentMethod: 'stripe' | 'google-pay' | 'apple-pay';
  cardDetails?: {
    cardNumber: string;
    expiryDate: string;
    cvv: string;
    cardholderName: string;
  };
  promoCode?: string;
  totalAmount: number;
}) => {
  const response = await api.post<ApiResponse<any>>('/api/payments', paymentData);
  return response.data;
};

// Get payment by ID
export const getPayment = async (paymentId: string) => {
  const response = await api.get<ApiResponse<any>>(`/api/payments/${paymentId}`);
  return response.data;
};
