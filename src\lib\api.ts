import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

// Console log the base URL configuration
console.log('🔧 API Configuration:');
console.log(`📍 Base URL: ${API_BASE_URL}`);
console.log(`📝 Source: ${process.env.NEXT_PUBLIC_API_URL ? 'NEXT_PUBLIC_API_URL environment variable' : 'Default fallback (localhost:3000)'}`);
console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
console.log(`🔍 All API-related env vars:`);
console.log(`   NEXT_PUBLIC_API_URL: ${process.env.NEXT_PUBLIC_API_URL || 'undefined'}`);
console.log(`   NEXT_PUBLIC_SITE_URL: ${process.env.NEXT_PUBLIC_SITE_URL || 'undefined'}`);
console.log(`   NODE_ENV: ${process.env.NODE_ENV || 'undefined'}`);

export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for debugging
api.interceptors.request.use(
  (config) => {
    const fullUrl = `${config.baseURL}${config.url}`;
    console.log('🚀 API Request Details:');
    console.log(`   Method: ${config.method?.toUpperCase()}`);
    console.log(`   Base URL: ${config.baseURL} (from axios config)`);
    console.log(`   Endpoint: ${config.url}`);
    console.log(`   Full URL: ${fullUrl}`);
    if (config.params) {
      console.log(`   Params:`, config.params);
    }
    if (config.data) {
      console.log(`   Data:`, config.data);
    }
    return config;
  },
  (error) => {
    console.error('❌ API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message);

    // Special handling for rate limiting
    if (error.response?.status === 429) {
      console.warn('Rate limit exceeded. Please wait before making more requests.');
    }

    return Promise.reject(error);
  }
);
