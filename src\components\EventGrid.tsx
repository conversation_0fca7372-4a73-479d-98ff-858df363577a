import Link from 'next/link';
import { format } from 'date-fns';
import { Calendar, MapPin, Users, Clock } from 'lucide-react';
import { Event } from '@/types';
import { LoadingSpinner } from './ui/LoadingSpinner';

interface EventGridProps {
  events: Event[];
  loading?: boolean;
  error?: any;
  emptyMessage?: string;
}

export function EventGrid({ events, loading, error, emptyMessage = 'No events found' }: EventGridProps) {
  if (loading) {
    return (
      <div className="text-center py-12">
        <LoadingSpinner size="lg" className="text-primary-500" />
        <p className="mt-4 text-gray-600">Loading events...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Failed to load events. Please try again later.</p>
      </div>
    );
  }

  if (events.length === 0) {
    return (
      <div className="text-center py-12">
        <Calendar className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <p className="text-gray-600 text-lg">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {events.map((event) => (
        <Link
          key={event._id}
          href={`/events/${event._id}/payment`}
          className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden group"
        >
          {/* Event Image */}
          <div className="aspect-video bg-gradient-to-br from-primary-500 to-primary-600 relative overflow-hidden">
            {event.image ? (
              <img
                src={event.image.url}
                alt={event.image.alt || event.title}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-white">
                <Calendar className="w-12 h-12" />
              </div>
            )}
            
            {/* Category Badge */}
            <div className="absolute top-3 left-3">
              <span className="bg-white/90 text-gray-900 px-2 py-1 rounded-full text-xs font-medium">
                {event.category}
              </span>
            </div>

            {/* Availability Badge */}
            <div className="absolute top-3 right-3">
              {event.availableTickets > 0 ? (
                <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                  Available
                </span>
              ) : (
                <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                  Sold Out
                </span>
              )}
            </div>
          </div>

          {/* Event Details */}
          <div className="p-4">
            <h3 className="font-semibold text-lg text-gray-900 mb-2 group-hover:text-primary-600 transition-colors line-clamp-2">
              {event.title}
            </h3>

            <div className="space-y-2 text-sm text-gray-600 mb-4">
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4" />
                <span>{format(new Date(event.dateTime), 'MMM dd, yyyy')}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4" />
                <span>{format(new Date(event.dateTime), 'h:mm a')}</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="w-4 h-4" />
                <span className="truncate">{event.location.venue}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4" />
                <span>{event.promoterId.name}</span>
              </div>
            </div>

            {/* Price and Availability */}
            <div className="flex items-center justify-between">
              {event.priceCategories && event.priceCategories.length > 0 ? (
                <div>
                  <span className="text-sm text-gray-500">From</span>
                  <span className="font-bold text-lg text-primary-600 ml-1">
                    ${Math.min(...event.priceCategories.map(cat => cat.price))}
                  </span>
                </div>
              ) : (
                <span className="text-gray-500">Price TBA</span>
              )}

              {event.availableTickets > 0 && (
                <span className="text-xs text-green-600">
                  {event.availableTickets} left
                </span>
              )}
            </div>

            {/* Description Preview */}
            {event.description && (
              <p className="text-sm text-gray-600 mt-3 line-clamp-2">
                {event.description}
              </p>
            )}
          </div>
        </Link>
      ))}
    </div>
  );
}
