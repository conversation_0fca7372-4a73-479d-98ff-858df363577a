'use client';

import Link from 'next/link';
import { format } from 'date-fns';
import { Calendar, MapPin, Users } from 'lucide-react';
import { useMemo } from 'react';
import { Event } from '@/types';
import { Button } from './ui/Button';
import { LoadingSpinner } from './ui/LoadingSpinner';
import { useEvents } from '@/hooks/useEvents';

interface UpcomingEventsProps {
  events?: Event[];
}

export function UpcomingEvents({ events: initialEvents }: UpcomingEventsProps) {
  // Memoize the current date to prevent constant re-renders
  const currentDate = useMemo(() => new Date().toISOString(), []);

  // Use React Query to fetch real data from API
  const { data, isLoading, error } = useEvents({
    limit: 8,
    dateFrom: currentDate
  });

  // Get events from API
  const events = data?.data?.data || [];

  if (isLoading) {
    return (
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <LoadingSpinner size="lg" className="text-primary-500" />
            <p className="mt-4 text-gray-600">Loading upcoming events...</p>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p className="text-red-600">Failed to load events. Please try again later.</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="font-display text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Upcoming Events
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Don't miss out on these amazing events happening soon
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {events.map((event) => (
            <Link
              key={event._id}
              href={`/events/${event._id}/payment`}
              className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden group"
            >
              {/* Event Image */}
              <div className="aspect-video bg-gradient-to-br from-primary-500 to-primary-600 relative overflow-hidden">
                {event.image ? (
                  <img
                    src={event.image.url}
                    alt={event.image.alt || event.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-white">
                    <Calendar className="w-12 h-12" />
                  </div>
                )}
                
                {/* Category Badge */}
                <div className="absolute top-3 left-3">
                  <span className="bg-white/90 text-gray-900 px-2 py-1 rounded-full text-xs font-medium">
                    {event.category}
                  </span>
                </div>
              </div>

              {/* Event Details */}
              <div className="p-4">
                <h3 className="font-semibold text-lg text-gray-900 mb-2 group-hover:text-primary-600 transition-colors line-clamp-2">
                  {event.title}
                </h3>

                <div className="space-y-2 text-sm text-gray-600 mb-4">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4" />
                    <span>{format(new Date(event.dateTime), 'MMM dd, yyyy')}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <MapPin className="w-4 h-4" />
                    <span className="truncate">{event.location.venue}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4" />
                    <span>{event.promoterId.name}</span>
                  </div>
                </div>

                {/* Price */}
                {event.priceCategories && event.priceCategories.length > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">From</span>
                    <span className="font-bold text-lg text-primary-600">
                      ${Math.min(...event.priceCategories.map(cat => cat.price))}
                    </span>
                  </div>
                )}

                {/* Availability */}
                {event.availableTickets > 0 ? (
                  <div className="mt-2 text-xs text-green-600">
                    {event.availableTickets} tickets available
                  </div>
                ) : (
                  <div className="mt-2 text-xs text-red-600">
                    Sold out
                  </div>
                )}
              </div>
            </Link>
          ))}
        </div>

        <div className="text-center">
          <Button variant="outline" size="lg">
            <Link href="/events">View All Events</Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
