import { ResolvedSettings } from '@/types';

/**
 * Apply portal settings as CSS variables to the document root
 */
export const applyCSSVariables = (resolvedSettings: ResolvedSettings) => {
  if (typeof document === 'undefined') return;

  const root = document.documentElement;
  const global = resolvedSettings.global;

  // Apply global settings as CSS variables
  const cssVars = {
    '--portal-theme': global.theme,
    '--portal-accent-color': global.accentColor, // For panel borders
    '--portal-border-width': `${global.borderWidth}px`,
    '--portal-background-color': global.backgroundColor.color, // Panel background
    '--portal-background-opacity': global.backgroundColor.opacity.toString(),
    '--portal-font-family': global.typography.fontFamily, // Whole page typography
    '--portal-corner-radius': global.panelSettings.cornerStyle === 'rounded' ? '0.375rem' : '0', // Panel border radius
  };

  // Set CSS variables on document root
  Object.entries(cssVars).forEach(([property, value]) => {
    root.style.setProperty(property, value);
  });
};

/**
 * Get background style object based on background options
 */
export const getBackgroundStyle = (backgroundOptions: any) => {
  if (!backgroundOptions) return {};

  switch (backgroundOptions.type) {
    case 'color':
      return { backgroundColor: backgroundOptions.value };
    case 'gradient':
      return { background: backgroundOptions.value };
    case 'image':
      return {
        backgroundImage: `url(${backgroundOptions.value})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      };
    case 'video':
      // Video backgrounds require special handling with video elements
      return { backgroundColor: '#000000' }; // Fallback
    default:
      return {};
  }
};

/**
 * Get conditional styles based on portal settings
 */
export const getConditionalStyles = (resolvedSettings: ResolvedSettings | undefined) => {
  if (!resolvedSettings) return {};

  const global = resolvedSettings.global;

  return {
    // Dynamic border based on width setting
    border: global.borderWidth > 0
      ? `${global.borderWidth}px solid ${global.accentColor}`
      : 'none',

    // Conditional corner radius
    borderRadius: global.panelSettings.cornerStyle === 'rounded'
      ? '0.375rem'
      : '0',

    // Theme-based text color
    color: global.theme === 'dark' ? '#ffffff' : '#000000',

    // Background with opacity
    backgroundColor: global.backgroundColor.color,
    opacity: global.backgroundColor.opacity,

    // Font family
    fontFamily: global.typography.fontFamily,
  };
};

/**
 * Get theme classes for conditional styling
 */
export const getThemeClasses = (theme: string, cornerStyle: string) => {
  const baseClasses = 'transition-all duration-200';
  const themeClasses = theme === 'dark' ? 'dark' : 'light';
  const cornerClasses = cornerStyle === 'rounded' ? 'rounded' : 'square';
  
  return `${baseClasses} theme-${themeClasses} corner-${cornerClasses}`;
};
