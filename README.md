# EventDiscover - Ticket System End User Frontend

A modern, responsive web application for discovering and booking tickets for events from trusted promoters. Built with Next.js, TypeScript, and Tailwind CSS.

## 🚀 Features

- **Event Discovery**: Browse and search for events by category, location, and date
- **Promoter Profiles**: Dedicated pages for event promoters with their event portfolios
- **Event Details**: Comprehensive event pages with ticket purchasing functionality
- **Responsive Design**: Mobile-first design that works on all devices
- **Real-time Data**: Live ticket availability and pricing information
- **SEO Optimized**: Server-side rendering and optimized meta tags
- **Accessibility**: WCAG compliant design with proper focus management

## 🛠️ Technology Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: TanStack Query (React Query) + Context API
- **HTTP Client**: Axios
- **Date Handling**: date-fns
- **Form Handling**: React Hook Form with Zod validation
- **Icons**: Lucide React
- **Testing**: Jest + React Testing Library

## 📦 Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd TicketSystemEndUserFrontEnd
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

Edit `.env.local` and add your configuration:
```env
NEXT_PUBLIC_API_URL=http://localhost:3000/api
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

4. Run the development server:
```bash
npm run dev
```

Open [http://localhost:3001](http://localhost:3001) with your browser to see the application.

## 🧪 Testing

Run the test suite:
```bash
npm test
```

Run tests in watch mode:
```bash
npm run test:watch
```

## 🏗️ Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── events/            # Event listing and detail pages
│   ├── promoters/         # Promoter listing and profile pages
│   └── globals.css        # Global styles
├── components/            # Reusable UI components
│   ├── ui/               # Basic UI components (Button, etc.)
│   └── __tests__/        # Component tests
├── contexts/             # React Context providers
├── hooks/                # Custom React hooks
├── lib/                  # Utility functions and configurations
├── providers/            # App-level providers
├── services/             # API service functions
└── types/                # TypeScript type definitions
```

## 🎨 Design System

The application follows a consistent design system with:

- **Colors**: Primary blue (#3b82f6), Secondary gray, Accent orange
- **Typography**: Inter for body text, Poppins for headings
- **Spacing**: Consistent spacing scale using Tailwind CSS
- **Components**: Reusable UI components with consistent styling

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm test` - Run tests
- `npm run test:watch` - Run tests in watch mode

## 📱 Pages

### Homepage (`/`)
- Hero section with location and category filters
- Featured promoters carousel
- Event categories

### Event Detail (`/events/[eventId]`)
- Comprehensive event information
- Ticket selection and purchasing
- Venue details and map
- Related events

### Promoters (`/promoters`)
- Promoter listing with search
- Promoter profiles and statistics

### Promoter Profile (`/promoters/[promoterId]`)
- Promoter information and social links
- Event tabs (upcoming/past)
- Event filtering and search

## 🔌 API Integration

The application integrates with a MongoDB-based backend API. Key endpoints:

- `GET /api/events/public` - Get public events
- `GET /api/events/public/:id` - Get single event
- `GET /api/promoters/public` - Get public promoters
- `GET /api/promoters/public/:id` - Get single promoter
- `GET /api/promoters/public/:id/events` - Get promoter events

## 🚀 Deployment

The application is optimized for deployment on Vercel:

1. Connect your repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

For other platforms, build the application:
```bash
npm run build
npm run start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
