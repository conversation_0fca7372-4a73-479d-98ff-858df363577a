'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useEvent } from '@/hooks/useEvents';
import { usePortalTheme } from '@/hooks/usePortalSettings';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Button } from '@/components/ui/Button';
import { DateContainer } from '@/components/DateContainer';
import { ExpandablePriceCategory } from '@/components/ExpandablePriceCategory';
import { LanguageSelector } from '@/components/LanguageSelector';
import { applyCSSVariables, getBackgroundStyle, getThemeClasses } from '@/lib/portalSettings';
import { Minus, Plus, Check } from 'lucide-react';
import { Event, PriceCategory, CategorySelection } from '@/types';
import { api } from '@/lib/api';



export default function PaymentPage() {
  const params = useParams();
  const router = useRouter();
  const eventId = params.eventId as string;
  
  // State management
  const [selectedCategories, setSelectedCategories] = useState<CategorySelection[]>([]);
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null);
  const [promoCode, setPromoCode] = useState('');
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Fetch event data
  const { data: eventData, isLoading: eventLoading, error: eventError } = useEvent(eventId);
  const event = eventData?.data;

  // Get promoter ID from event data to fetch portal settings
  const promoterId = event?.promoterId?._id;

  // Fetch portal settings for the event's promoter
  const {
    resolvedSettings,
    isLoading: settingsLoading,
    theme,
    accentColor,
    borderWidth,
    cornerStyle,
    fontFamily,
    backgroundColor,
    eventPageSettings,
  } = usePortalTheme(promoterId || '');

  // Apply CSS variables when settings are loaded
  useEffect(() => {
    if (resolvedSettings) {
      applyCSSVariables(resolvedSettings);
    }
  }, [resolvedSettings]);

  // Calculate totals
  const calculateTotal = () => {
    if (!event) return 0;
    return selectedCategories.reduce((total, selection) => {
      const category = event.priceCategories.find(cat => cat._id === selection.categoryId);
      return total + (category ? category.price * selection.quantity : 0);
    }, 0);
  };

  const totalAmount = calculateTotal();
  const hasSelections = selectedCategories.length > 0;

  // Handle category selection
  const handleCategoryToggle = (categoryId: string) => {
    if (expandedCategory === categoryId) {
      setExpandedCategory(null);
    } else {
      setExpandedCategory(categoryId);
      // Initialize with 1 ticket if not already selected
      if (!selectedCategories.find(sel => sel.categoryId === categoryId)) {
        setSelectedCategories(prev => [...prev, { categoryId, quantity: 1 }]);
      }
    }
  };

  const handleQuantityChange = (categoryId: string, quantity: number) => {
    if (quantity === 0) {
      setSelectedCategories(prev => prev.filter(sel => sel.categoryId !== categoryId));
    } else {
      setSelectedCategories(prev => {
        const existing = prev.find(sel => sel.categoryId === categoryId);
        if (existing) {
          return prev.map(sel =>
            sel.categoryId === categoryId ? { ...sel, quantity } : sel
          );
        } else {
          return [...prev, { categoryId, quantity }];
        }
      });
    }
  };

  const handleProceedToPayment = async () => {
    if (!hasSelections || !agreeToTerms) return;

    setIsProcessing(true);
    try {
      // Prepare payment data for API
      const paymentData = {
        eventId,
        selectedCategories,
        promoCode: promoCode || undefined
      };

      // Send POST request to /api/payments
      const response = await api.post('/api/payments', paymentData);

      if (response.data.success) {
        // Store only the payment ID for the checkout page
        const paymentId = response.data.data._id || response.data.data.id;
        sessionStorage.setItem('paymentId', paymentId);

        // Navigate to checkout page
        router.push(`/events/${eventId}/checkout`);
      } else {
        throw new Error(response.data.message || 'Failed to process payment data');
      }
    } catch (error: unknown) {
      console.error('Payment API error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to proceed. Please try again.';
      alert(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  // Show loading state while fetching event or settings
  if (eventLoading || settingsLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <LoadingSpinner size="lg" className="text-white mb-4" />
          <p>Loading event details...</p>
        </div>
      </div>
    );
  }

  if (eventError || !event) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <h1 className="text-2xl font-bold mb-4">Event Not Found</h1>
          <p>The event you're looking for doesn't exist or has been removed.</p>
        </div>
      </div>
    );
  }

  // Fallback portal settings for demonstration if API fails
  const fallbackSettings = {
    theme: 'light',
    accentColor: '#f43f5e', // Rose
    borderWidth: 1,
    cornerStyle: 'square',
    fontFamily: 'Arial, sans-serif',
    backgroundColor: { color: '#06b6d4', opacity: 1 }, // Cyan
    eventPageSettings: {
      backgroundOptions: { type: 'color', value: '#3b82f6' }, // Blue
      panelSettings: {
        backgroundColor: { color: '#06b6d4', opacity: 1 }, // Cyan
        cornerStyle: 'square'
      }
    }
  };

  // Use fallback if portal settings are not loaded
  const effectiveTheme = theme || fallbackSettings.theme;
  const effectiveAccentColor = accentColor || fallbackSettings.accentColor;
  const effectiveBorderWidth = borderWidth || fallbackSettings.borderWidth;
  const effectiveCornerStyle = cornerStyle || fallbackSettings.cornerStyle;
  const effectiveFontFamily = fontFamily || fallbackSettings.fontFamily;
  const effectiveBackgroundColor = backgroundColor || fallbackSettings.backgroundColor;
  const effectiveEventPageSettings = eventPageSettings || fallbackSettings.eventPageSettings;

  // Get background style from event page settings
  const pageBackgroundStyle = effectiveEventPageSettings?.backgroundOptions
    ? getBackgroundStyle(effectiveEventPageSettings.backgroundOptions)
    : {};

  return (
    <div
      className={`min-h-screen flex items-center justify-center p-2 ${getThemeClasses(effectiveTheme, effectiveCornerStyle)}`}
      style={{
        ...pageBackgroundStyle,
        fontFamily: effectiveFontFamily, // Typography applies to whole page
      }}
    >
      <div
        className="w-full md:w-2/5 min-[48rem]:w-[28%] max-w-[800px] max-h-[95vh] mx-auto shadow-2xl overflow-hidden flex flex-col"
        style={{
          backgroundColor: effectiveBackgroundColor.color, // Panel background
          opacity: effectiveBackgroundColor.opacity,
          border: `${effectiveBorderWidth}px solid ${effectiveAccentColor}`, // Panel border with accent color
          borderRadius: effectiveCornerStyle === 'rounded' ? '0.375rem' : '0', // Panel border radius
        }}
      >
        
        {/* Event Information Section */}
        <div
          className="p-3 min-[48rem]:p-4 flex-shrink-0"
          style={{
            borderBottom: `${effectiveBorderWidth}px solid ${effectiveAccentColor}`,
          }}
        >
          <div className="flex gap-2 min-[48rem]:gap-3">
            <DateContainer dateTime={event.dateTime} />
            <div className="flex-1">
              <h1 className={`text-lg min-[48rem]:text-xl font-bold ${effectiveTheme === 'dark' ? 'text-white' : 'text-black'}`}>
                {event.title}
              </h1>
              <p className={`text-xs min-[48rem]:text-sm ${effectiveTheme === 'dark' ? 'text-gray-300' : 'text-gray-800'}`}>
                {event.location.venue}
              </p>
              <p className={`text-xs line-clamp-2 ${effectiveTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                {event.description}
              </p>
            </div>
          </div>
        </div>

        {/* Price Categories and Promo Code Section */}
        <div className="flex-1 min-h-0 flex flex-col">
          {/* Price Categories */}
          <div className="p-3 min-[48rem]:p-4 flex-1 overflow-y-auto">
            <h2 className={`text-sm min-[48rem]:text-base font-semibold mb-2 ${effectiveTheme === 'dark' ? 'text-white' : 'text-black'}`}>
              Select Tickets
            </h2>
            <div className="space-y-1">
              {event.priceCategories.map((category) => (
                <ExpandablePriceCategory
                  key={category._id}
                  category={category}
                  isExpanded={expandedCategory === category._id}
                  onToggle={() => handleCategoryToggle(category._id)}
                  quantity={selectedCategories.find(sel => sel.categoryId === category._id)?.quantity || 0}
                  onQuantityChange={(quantity) => handleQuantityChange(category._id, quantity)}
                />
              ))}
            </div>
          </div>

          {/* Promo Code Section */}
          <div
            className="p-3 min-[48rem]:p-4 flex-shrink-0"
            style={{
              borderTop: `${effectiveBorderWidth}px solid ${effectiveAccentColor}`,
            }}
          >
            <div className="flex items-center gap-2">
              <label
                htmlFor="promoCode"
                className={`text-xs min-[48rem]:text-sm font-medium whitespace-nowrap ${effectiveTheme === 'dark' ? 'text-white' : 'text-black'}`}
              >
                Promo Code:
              </label>
              <input
                type="text"
                id="promoCode"
                value={promoCode}
                onChange={(e) => setPromoCode(e.target.value)}
                placeholder="Enter code"
                className={`flex-1 px-2 py-1 text-xs min-[48rem]:text-sm outline-none transition-all duration-300 ${
                  effectiveTheme === 'dark' ? 'bg-gray-800 text-white placeholder-gray-400' : 'bg-white text-black placeholder-gray-500'
                }`}
                style={{
                  border: `${effectiveBorderWidth}px solid ${effectiveAccentColor}`,
                  borderRadius: effectiveCornerStyle === 'rounded' ? '0.375rem' : '0',
                }}
                onFocus={(e) => {
                  e.target.style.boxShadow = `0 0 10px ${effectiveAccentColor}`;
                }}
                onBlur={(e) => {
                  e.target.style.boxShadow = 'none';
                }}
              />
            </div>
          </div>
        </div>

        {/* Payment Summary Section */}
        <div
          className="p-3 min-[48rem]:p-4 flex-shrink-0"
          style={{
            borderBottom: `${effectiveBorderWidth}px solid ${effectiveAccentColor}`,
          }}
        >
          <div className="flex justify-between items-center mb-2">
            <span className={`text-sm min-[48rem]:text-base font-semibold ${effectiveTheme === 'dark' ? 'text-white' : 'text-black'}`}>
              Total
            </span>
            <span className={`text-lg min-[48rem]:text-xl font-bold ${effectiveTheme === 'dark' ? 'text-white' : 'text-black'}`}>
              ${totalAmount.toFixed(2)}
            </span>
          </div>

          <Button
            onClick={handleProceedToPayment}
            loading={isProcessing}
            disabled={!hasSelections || !agreeToTerms}
            className="w-full mb-2 h-10 transition-all duration-300 text-white disabled:opacity-50"
            style={{
              backgroundColor: effectiveAccentColor,
              border: `${effectiveBorderWidth}px solid ${effectiveAccentColor}`,
              borderRadius: effectiveCornerStyle === 'rounded' ? '0.375rem' : '0',
            }}
            onMouseEnter={(e) => {
              if (!isProcessing && hasSelections && agreeToTerms) {
                e.currentTarget.style.opacity = '0.8';
                e.currentTarget.style.boxShadow = `0 0 12px ${effectiveAccentColor}`;
              }
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.opacity = '1';
              e.currentTarget.style.boxShadow = 'none';
            }}
            size="md"
          >
            {isProcessing ? 'Processing...' : 'Next'}
          </Button>

          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="terms"
              checked={agreeToTerms}
              onChange={(e) => setAgreeToTerms(e.target.checked)}
              className="w-3 h-3 min-[48rem]:w-4 min-[48rem]:h-4 transition-all duration-300"
              style={{
                accentColor: effectiveAccentColor,
                border: `${effectiveBorderWidth}px solid ${effectiveAccentColor}`,
              }}
              onFocus={(e) => {
                e.target.style.boxShadow = `0 0 8px ${effectiveAccentColor}`;
              }}
              onBlur={(e) => {
                e.target.style.boxShadow = 'none';
              }}
            />
            <label
              htmlFor="terms"
              className={`text-xs cursor-pointer transition-all duration-300 ${
                effectiveTheme === 'dark' ? 'text-gray-300' : 'text-gray-800'
              }`}
              onMouseEnter={(e) => {
                (e.target as HTMLElement).style.textShadow = `0 0 6px ${effectiveAccentColor}`;
              }}
              onMouseLeave={(e) => {
                (e.target as HTMLElement).style.textShadow = 'none';
              }}
            >
              I agree to the{' '}
              <a
                href="#"
                className={`font-medium hover:underline transition-all duration-300 ${
                  effectiveTheme === 'dark' ? 'text-white' : 'text-black'
                }`}
                style={{ color: effectiveAccentColor }}
                onMouseEnter={(e) => {
                  (e.target as HTMLElement).style.textShadow = `0 0 6px ${effectiveAccentColor}`;
                }}
                onMouseLeave={(e) => {
                  (e.target as HTMLElement).style.textShadow = 'none';
                }}
              >
                service terms
              </a>{' '}
              and conditions
            </label>
          </div>
        </div>

        {/* Footer Section */}
        <div
          className="p-2 min-[48rem]:p-3 flex justify-between items-center flex-shrink-0"
          style={{
            backgroundColor: effectiveTheme === 'dark' ? 'rgba(0,0,0,0.2)' : 'rgba(255,255,255,0.2)',
            borderTop: `${effectiveBorderWidth}px solid ${effectiveAccentColor}`,
          }}
        >
          <span className={`text-xs ${effectiveTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
            Powered by EventDiscover
          </span>
          <LanguageSelector />
        </div>
      </div>
    </div>
  );
}
