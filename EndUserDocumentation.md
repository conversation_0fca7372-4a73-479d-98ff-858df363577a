# End User Website Documentation - Promoter Events Viewer

## Overview

This documentation provides comprehensive guidance for building a public-facing website that allows end users to discover and view events from specific promoters. The website integrates with the existing MongoDB-based ticket system backend to provide a seamless event browsing and ticket purchasing experience.

## Table of Contents

1. [Website Purpose & Features](#website-purpose--features)
2. [Technology Stack & Setup](#technology-stack--setup)
3. [API Integration](#api-integration)
4. [Required Backend Enhancements](#required-backend-enhancements)
5. [User Interface Design](#user-interface-design)
6. [Core Pages & Components](#core-pages--components)
7. [State Management](#state-management)
8. [Performance Optimization](#performance-optimization)
9. [SEO & Accessibility](#seo--accessibility)
10. [Testing Strategy](#testing-strategy)

## Website Purpose & Features

### Primary Goal
Create a user-friendly platform where end users can:
- Discover events from specific promoters
- Browse promoter profiles and their event portfolios
- View detailed event information including pricing and availability
- Purchase tickets directly through the platform

### Key Features
- **Promoter Discovery**: Browse and search for event promoters
- **Promoter Profiles**: Dedicated pages showing promoter information and their events
- **Event Listings**: Comprehensive event browsing with filters and search
- **Event Details**: Detailed event pages with pricing, location, and ticket purchasing
- **Responsive Design**: Mobile-first approach for optimal user experience
- **Real-time Updates**: Live ticket availability and pricing information
- **Social Integration**: Share events and promoter profiles on social media

## Technology Stack & Setup

### Recommended Framework: Next.js with TypeScript

**Why Next.js?**
- Server-side rendering (SSR) for better SEO
- Static site generation (SSG) for performance
- Built-in API routes for backend integration
- Excellent TypeScript support
- Image optimization and performance features
- Great developer experience

### Core Dependencies

```json
{
  "dependencies": {
    "next": "^14.0.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "typescript": "^5.0.0",
    "@types/react": "^18.0.0",
    "@types/node": "^20.0.0",
    "tailwindcss": "^3.3.0",
    "axios": "^1.6.0",
    "react-query": "^3.39.0",
    "date-fns": "^2.30.0",
    "react-hook-form": "^7.47.0",
    "zod": "^3.22.0",
    "lucide-react": "^0.292.0",
    "next-seo": "^6.4.0"
  },
  "devDependencies": {
    "@types/react-dom": "^18.0.0",
    "eslint": "^8.0.0",
    "eslint-config-next": "^14.0.0",
    "prettier": "^3.0.0",
    "@testing-library/react": "^13.4.0",
    "@testing-library/jest-dom": "^6.1.0",
    "jest": "^29.7.0"
  }
}
```

### Project Setup

```bash
# Create Next.js project
npx create-next-app@latest event-discovery --typescript --tailwind --eslint --app

# Install additional dependencies
npm install axios react-query date-fns react-hook-form zod lucide-react next-seo

# Install development dependencies
npm install -D @testing-library/react @testing-library/jest-dom jest
```

## API Integration

### Base Configuration

```typescript
// lib/api.ts
import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';

export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);
```

### API Service Functions

```typescript
// services/eventService.ts
import { api } from '@/lib/api';

export interface Event {
  _id: string;
  title: string;
  description: string;
  dateTime: string;
  location: {
    venue: string;
    address: string;
    city: string;
    coordinates: [number, number];
  };
  image?: {
    url: string;
    alt: string;
  };
  promoterId: {
    _id: string;
    name: string;
    socials?: {
      website?: string;
      facebook?: string;
      instagram?: string;
      twitter?: string;
    };
  };
  priceCategories: PriceCategory[];
  category: string;
  tags: string[];
  capacity?: number;
  ageRestriction?: number;
  salesActive: boolean;
  availableTickets: number;
}

export interface PriceCategory {
  _id: string;
  name: string;
  price: number;
  totalTickets: number;
  soldTickets: number;
  availableTickets: number;
  description?: string;
}

export interface Promoter {
  _id: string;
  name: string;
  socials: {
    website?: string;
    facebook?: string;
    instagram?: string;
    twitter?: string;
  };
  createdAt: string;
}

// Get public events with filters
export const getPublicEvents = async (params: {
  page?: number;
  limit?: number;
  category?: string;
  city?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
  promoterId?: string; // New filter for promoter-specific events
}) => {
  const response = await api.get('/events/public', { params });
  return response.data;
};

// Get single public event
export const getPublicEvent = async (eventId: string) => {
  const response = await api.get(`/events/public/${eventId}`);
  return response.data;
};

// Get available price categories for an event
export const getAvailableCategories = async (eventId: string) => {
  const response = await api.get(`/events/${eventId}/categories/available`);
  return response.data;
};

// Purchase tickets (to be implemented)
export const purchaseTickets = async (ticketData: {
  eventId: string;
  categoryId: string;
  quantity: number;
  customerInfo: {
    name: string;
    email: string;
    phone?: string;
  };
}) => {
  const response = await api.post('/tickets/purchase', ticketData);
  return response.data;
};
```

## Required Backend Enhancements

To support the end user website, the following endpoints need to be added to the backend:

### 1. Public Promoter Endpoints

```javascript
// New routes to add to src/routes/promoter.js or create src/routes/publicPromoter.js

/**
 * @route   GET /api/promoters/public
 * @desc    Get list of public promoters
 * @access  Public
 */
router.get('/public', promoterController.getPublicPromoters);

/**
 * @route   GET /api/promoters/public/:promoterId
 * @desc    Get single public promoter profile
 * @access  Public
 */
router.get('/public/:promoterId', promoterController.getPublicPromoter);

/**
 * @route   GET /api/promoters/public/:promoterId/events
 * @desc    Get events by specific promoter
 * @access  Public
 */
router.get('/public/:promoterId/events', promoterController.getPromoterPublicEvents);
```

### 2. Enhanced Event Filtering

The existing `/api/events/public` endpoint should be enhanced to support:
- `promoterId` query parameter for filtering events by promoter
- Better search functionality across promoter names
- Featured events support

### 3. Promoter Controller Methods

```javascript
// New methods to add to src/controllers/promoterController.js

/**
 * Get public promoters list
 */
async getPublicPromoters(req, res) {
  try {
    const { page = 1, limit = 12, search } = req.query;
    
    const query = { isActive: true };
    if (search) {
      query.$text = { $search: search };
    }
    
    const promoters = await EventPromoter.find(query)
      .select('name socials createdAt')
      .skip((page - 1) * limit)
      .limit(parseInt(limit))
      .sort({ name: 1 });
    
    const totalPromoters = await EventPromoter.countDocuments(query);
    
    res.json({
      success: true,
      data: {
        promoters,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalPromoters / limit),
          totalPromoters
        }
      }
    });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Failed to get promoters' });
  }
}

/**
 * Get single public promoter
 */
async getPublicPromoter(req, res) {
  try {
    const { promoterId } = req.params;
    
    const promoter = await EventPromoter.findOne({
      _id: promoterId,
      isActive: true
    }).select('name socials createdAt');
    
    if (!promoter) {
      return res.status(404).json({
        success: false,
        message: 'Promoter not found'
      });
    }
    
    // Get promoter's event statistics
    const totalEvents = await Event.countDocuments({
      promoterId,
      isPublic: true,
      status: 'published'
    });
    
    const upcomingEvents = await Event.countDocuments({
      promoterId,
      isPublic: true,
      status: 'published',
      dateTime: { $gte: new Date() }
    });
    
    res.json({
      success: true,
      data: {
        ...promoter.toObject(),
        stats: {
          totalEvents,
          upcomingEvents
        }
      }
    });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Failed to get promoter' });
  }
}

/**
 * Get promoter's public events
 */
async getPromoterPublicEvents(req, res) {
  try {
    const { promoterId } = req.params;
    const { page = 1, limit = 10, status = 'upcoming' } = req.query;
    
    // Build query based on status filter
    const query = {
      promoterId,
      isPublic: true,
      status: 'published'
    };
    
    if (status === 'upcoming') {
      query.dateTime = { $gte: new Date() };
    } else if (status === 'past') {
      query.dateTime = { $lt: new Date() };
    }
    
    const events = await Event.find(query)
      .populate('priceCategories')
      .sort({ dateTime: status === 'past' ? -1 : 1 })
      .skip((page - 1) * limit)
      .limit(parseInt(limit));
    
    const totalEvents = await Event.countDocuments(query);
    
    res.json({
      success: true,
      data: {
        events,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalEvents / limit),
          totalEvents
        }
      }
    });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Failed to get promoter events' });
  }
}
```

## User Interface Design

### Design Principles

1. **Mobile-First**: Design for mobile devices first, then scale up
2. **Clean & Minimal**: Focus on content and ease of navigation
3. **Fast Loading**: Optimize for performance and quick page loads
4. **Accessible**: Follow WCAG guidelines for accessibility
5. **Intuitive Navigation**: Clear information hierarchy and user flows

### Color Scheme & Typography

```css
/* tailwind.config.js */
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        },
        secondary: {
          50: '#f8fafc',
          500: '#64748b',
          600: '#475569',
        },
        accent: {
          500: '#f59e0b',
          600: '#d97706',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        display: ['Poppins', 'system-ui', 'sans-serif'],
      }
    }
  }
}
```

### Layout Structure

```
┌─────────────────────────────────────┐
│              Header                 │
│  Logo | Navigation | Search | CTA   │
├─────────────────────────────────────┤
│                                     │
│            Main Content             │
│                                     │
├─────────────────────────────────────┤
│              Footer                 │
│    Links | Social | Newsletter     │
└─────────────────────────────────────┘
```

## Core Pages & Components

### 1. Homepage (`/`)

**Purpose**: Welcome users and showcase featured promoters and events

**Components**:
- Hero section with search functionality
- Featured promoters carousel
- Upcoming events grid
- Event categories
- Newsletter signup

```typescript
// pages/index.tsx
import { GetStaticProps } from 'next';
import { HeroSection } from '@/components/HeroSection';
import { FeaturedPromoters } from '@/components/FeaturedPromoters';
import { UpcomingEvents } from '@/components/UpcomingEvents';
import { EventCategories } from '@/components/EventCategories';

interface HomePageProps {
  featuredPromoters: Promoter[];
  upcomingEvents: Event[];
  categories: string[];
}

export default function HomePage({
  featuredPromoters,
  upcomingEvents,
  categories
}: HomePageProps) {
  return (
    <div className="min-h-screen">
      <HeroSection />
      <FeaturedPromoters promoters={featuredPromoters} />
      <UpcomingEvents events={upcomingEvents} />
      <EventCategories categories={categories} />
    </div>
  );
}

export const getStaticProps: GetStaticProps = async () => {
  // Fetch data at build time for better performance
  const [featuredPromoters, upcomingEvents] = await Promise.all([
    getPublicPromoters({ limit: 6 }),
    getPublicEvents({ limit: 8, featured: true })
  ]);

  return {
    props: {
      featuredPromoters: featuredPromoters.data.promoters,
      upcomingEvents: upcomingEvents.data.events,
      categories: ['music', 'sports', 'theater', 'comedy', 'conference', 'workshop']
    },
    revalidate: 3600 // Revalidate every hour
  };
};
```

### 2. Promoter Profile Page (`/promoters/[promoterId]`)

**Purpose**: Display promoter information and their events

**Components**:
- Promoter header with name, bio, and social links
- Event tabs (Upcoming, Past)
- Event grid with filtering
- Contact/follow buttons

```typescript
// pages/promoters/[promoterId].tsx
import { GetServerSideProps } from 'next';
import { useState } from 'react';
import { PromoterHeader } from '@/components/PromoterHeader';
import { EventGrid } from '@/components/EventGrid';
import { EventFilters } from '@/components/EventFilters';

interface PromoterPageProps {
  promoter: Promoter & { stats: { totalEvents: number; upcomingEvents: number } };
  initialEvents: Event[];
}

export default function PromoterPage({ promoter, initialEvents }: PromoterPageProps) {
  const [events, setEvents] = useState(initialEvents);
  const [activeTab, setActiveTab] = useState<'upcoming' | 'past'>('upcoming');
  const [filters, setFilters] = useState({});

  return (
    <div className="min-h-screen">
      <PromoterHeader promoter={promoter} />

      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          <aside className="lg:w-1/4">
            <EventFilters
              onFiltersChange={setFilters}
              categories={['music', 'sports', 'theater']}
            />
          </aside>

          <main className="lg:w-3/4">
            <div className="mb-6">
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8">
                  <button
                    onClick={() => setActiveTab('upcoming')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'upcoming'
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Upcoming Events ({promoter.stats.upcomingEvents})
                  </button>
                  <button
                    onClick={() => setActiveTab('past')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'past'
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Past Events
                  </button>
                </nav>
              </div>
            </div>

            <EventGrid
              events={events}
              loading={false}
              emptyMessage={`No ${activeTab} events found`}
            />
          </main>
        </div>
      </div>
    </div>
  );
}

export const getServerSideProps: GetServerSideProps = async ({ params }) => {
  const promoterId = params?.promoterId as string;

  try {
    const [promoterResponse, eventsResponse] = await Promise.all([
      getPublicPromoter(promoterId),
      getPromoterPublicEvents(promoterId, { status: 'upcoming', limit: 12 })
    ]);

    return {
      props: {
        promoter: promoterResponse.data,
        initialEvents: eventsResponse.data.events
      }
    };
  } catch (error) {
    return {
      notFound: true
    };
  }
};
```

### 3. Event Detail Page (`/events/[eventId]`)

**Purpose**: Show comprehensive event information and enable ticket purchasing

**Components**:
- Event hero with image and key details
- Event description and details
- Pricing and ticket selection
- Location and venue information
- Promoter information
- Related events

```typescript
// pages/events/[eventId].tsx
import { GetServerSideProps } from 'next';
import { useState } from 'react';
import { EventHero } from '@/components/EventHero';
import { EventDetails } from '@/components/EventDetails';
import { TicketSelection } from '@/components/TicketSelection';
import { VenueInfo } from '@/components/VenueInfo';
import { PromoterCard } from '@/components/PromoterCard';

interface EventPageProps {
  event: Event;
  relatedEvents: Event[];
}

export default function EventPage({ event, relatedEvents }: EventPageProps) {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [quantity, setQuantity] = useState(1);

  return (
    <div className="min-h-screen">
      <EventHero event={event} />

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <EventDetails event={event} />
            <VenueInfo location={event.location} />
          </div>

          <div className="lg:col-span-1">
            <div className="sticky top-4 space-y-6">
              <TicketSelection
                event={event}
                selectedCategory={selectedCategory}
                onCategorySelect={setSelectedCategory}
                quantity={quantity}
                onQuantityChange={setQuantity}
              />

              <PromoterCard promoter={event.promoterId} />
            </div>
          </div>
        </div>

        {relatedEvents.length > 0 && (
          <section className="mt-12">
            <h2 className="text-2xl font-bold mb-6">More from {event.promoterId.name}</h2>
            <EventGrid events={relatedEvents} />
          </section>
        )}
      </div>
    </div>
  );
}

export const getServerSideProps: GetServerSideProps = async ({ params }) => {
  const eventId = params?.eventId as string;

  try {
    const eventResponse = await getPublicEvent(eventId);
    const event = eventResponse.data;

    // Get related events from the same promoter
    const relatedEventsResponse = await getPublicEvents({
      promoterId: event.promoterId._id,
      limit: 4
    });

    // Filter out the current event
    const relatedEvents = relatedEventsResponse.data.events.filter(
      (e: Event) => e._id !== eventId
    );

    return {
      props: {
        event,
        relatedEvents
      }
    };
  } catch (error) {
    return {
      notFound: true
    };
  }
};
```

## State Management

### React Query for Server State

```typescript
// hooks/usePromoters.ts
import { useQuery } from 'react-query';
import { getPublicPromoters } from '@/services/eventService';

interface UsePromotersParams {
  page?: number;
  limit?: number;
  search?: string;
}

export const usePromoters = (params: UsePromotersParams = {}) => {
  return useQuery(
    ['promoters', params],
    () => getPublicPromoters(params),
    {
      keepPreviousData: true,
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    }
  );
};

// hooks/useEvents.ts
import { useQuery } from 'react-query';
import { getPublicEvents } from '@/services/eventService';

interface UseEventsParams {
  page?: number;
  limit?: number;
  category?: string;
  city?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
  promoterId?: string;
}

export const useEvents = (params: UseEventsParams = {}) => {
  return useQuery(
    ['events', params],
    () => getPublicEvents(params),
    {
      keepPreviousData: true,
      staleTime: 2 * 60 * 1000, // 2 minutes
      cacheTime: 5 * 60 * 1000, // 5 minutes
    }
  );
};

// hooks/useEvent.ts
import { useQuery } from 'react-query';
import { getPublicEvent } from '@/services/eventService';

export const useEvent = (eventId: string) => {
  return useQuery(
    ['event', eventId],
    () => getPublicEvent(eventId),
    {
      enabled: !!eventId,
      staleTime: 1 * 60 * 1000, // 1 minute
      cacheTime: 5 * 60 * 1000, // 5 minutes
    }
  );
};
```

### Context for Global State

```typescript
// contexts/AppContext.tsx
import { createContext, useContext, useReducer, ReactNode } from 'react';

interface AppState {
  searchFilters: {
    category?: string;
    city?: string;
    dateRange?: { from: string; to: string };
  };
  cart: {
    eventId?: string;
    categoryId?: string;
    quantity: number;
  } | null;
}

type AppAction =
  | { type: 'SET_SEARCH_FILTERS'; payload: Partial<AppState['searchFilters']> }
  | { type: 'SET_CART'; payload: AppState['cart'] }
  | { type: 'CLEAR_CART' };

const initialState: AppState = {
  searchFilters: {},
  cart: null
};

const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);

function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_SEARCH_FILTERS':
      return {
        ...state,
        searchFilters: { ...state.searchFilters, ...action.payload }
      };
    case 'SET_CART':
      return {
        ...state,
        cart: action.payload
      };
    case 'CLEAR_CART':
      return {
        ...state,
        cart: null
      };
    default:
      return state;
  }
}

export function AppProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
}

export function useAppContext() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within AppProvider');
  }
  return context;
}
```
