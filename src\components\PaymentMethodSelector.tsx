'use client';

import { useState } from 'react';
import { CreditCard, Smartphone } from 'lucide-react';
import { Input } from './ui/Input';

export type PaymentMethod = 'stripe' | 'google-pay' | 'apple-pay';

interface PaymentMethodSelectorProps {
  selectedMethod: PaymentMethod | null;
  onMethodChange: (method: PaymentMethod) => void;
  cardDetails: {
    cardNumber: string;
    expiryDate: string;
    cvv: string;
    cardholderName: string;
  };
  onCardDetailsChange: (details: any) => void;
}

export function PaymentMethodSelector({
  selectedMethod,
  onMethodChange,
  cardDetails,
  onCardDetailsChange
}: PaymentMethodSelectorProps) {
  const paymentMethods = [
    {
      id: 'stripe' as PaymentMethod,
      name: 'Credit/Debit Card',
      icon: CreditCard,
      description: 'Pay with your credit or debit card'
    },
    {
      id: 'google-pay' as PaymentMethod,
      name: 'Google Pay',
      icon: Smartphone,
      description: 'Pay with Google Pay'
    },
    {
      id: 'apple-pay' as PaymentMethod,
      name: 'Apple Pay',
      icon: Smartphone,
      description: 'Pay with Apple Pay'
    }
  ];

  const handleCardDetailChange = (field: string, value: string) => {
    onCardDetailsChange({
      ...cardDetails,
      [field]: value
    });
  };

  return (
    <div className="space-y-3">
      <h3 className="text-sm font-semibold text-black mb-2">Payment Method</h3>
      
      {/* Payment Method Options */}
      <div className="space-y-2">
        {paymentMethods.map((method) => {
          const Icon = method.icon;
          const isSelected = selectedMethod === method.id;
          
          return (
            <div key={method.id} className="border border-gray-900 overflow-hidden">
              <div
                className={`p-3 cursor-pointer transition-all duration-300 ${
                  isSelected 
                    ? 'bg-gray-100 border-b border-gray-900' 
                    : 'hover:bg-gray-50 hover:shadow-[0_0_8px_rgba(0,123,255,0.4)]'
                }`}
                onClick={() => onMethodChange(method.id)}
              >
                <div className="flex items-center gap-3">
                  <div className={`w-4 h-4 border-2 border-gray-900 rounded-full flex items-center justify-center ${
                    isSelected ? 'bg-black' : ''
                  }`}>
                    {isSelected && <div className="w-2 h-2 bg-white rounded-full" />}
                  </div>
                  <Icon className="w-5 h-5 text-black" />
                  <div className="flex-1">
                    <h4 className="font-medium text-black text-sm">{method.name}</h4>
                    <p className="text-xs text-gray-600">{method.description}</p>
                  </div>
                </div>
              </div>

              {/* Card Details Form - Only show for Stripe */}
              {isSelected && method.id === 'stripe' && (
                <div className="p-3 bg-white border-t border-gray-900 space-y-3">
                  <Input
                    label="Cardholder Name"
                    value={cardDetails.cardholderName}
                    onChange={(e) => handleCardDetailChange('cardholderName', e.target.value)}
                    placeholder="John Doe"
                    className="text-sm"
                  />
                  <Input
                    label="Card Number"
                    value={cardDetails.cardNumber}
                    onChange={(e) => handleCardDetailChange('cardNumber', e.target.value)}
                    placeholder="1234 5678 9012 3456"
                    className="text-sm"
                  />
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      label="Expiry Date"
                      value={cardDetails.expiryDate}
                      onChange={(e) => handleCardDetailChange('expiryDate', e.target.value)}
                      placeholder="MM/YY"
                      className="text-sm"
                    />
                    <Input
                      label="CVV"
                      value={cardDetails.cvv}
                      onChange={(e) => handleCardDetailChange('cvv', e.target.value)}
                      placeholder="123"
                      className="text-sm"
                    />
                  </div>
                </div>
              )}

              {/* Google Pay/Apple Pay - Show ready message */}
              {isSelected && (method.id === 'google-pay' || method.id === 'apple-pay') && (
                <div className="p-3 bg-white border-t border-gray-900">
                  <p className="text-sm text-gray-600">
                    {method.id === 'google-pay' ? 'Google Pay' : 'Apple Pay'} will be activated when you place your order.
                  </p>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
