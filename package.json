{"name": "ticket-system-end-user-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3001", "build": "next build", "start": "node server.js", "deploy:package": "node create-deployment.js", "deploy:build": "npm run build && npm run deploy:package", "deploy": "node deploy.js", "deploy:full": "npm run deploy:build && npm run deploy", "deploy:ftp": "node deploy-ftp.js", "deploy:ftp:full": "npm run build && npm run deploy:ftp", "deploy:ftp:simple": "node deploy-ftp-simple.js", "deploy:ftp:simple:full": "npm run build && npm run deploy:ftp:simple", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@tanstack/react-query": "^5.84.1", "axios": "^1.11.0", "basic-ftp": "^5.0.5", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "lucide-react": "^0.536.0", "next": "15.4.5", "next-seo": "^6.8.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "tailwindcss": "^4", "typescript": "^5"}}