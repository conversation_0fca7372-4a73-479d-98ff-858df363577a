import { NextSeo } from 'next-seo';

interface SEOProps {
  title?: string;
  description?: string;
  canonical?: string;
  openGraph?: {
    title?: string;
    description?: string;
    images?: Array<{
      url: string;
      width?: number;
      height?: number;
      alt?: string;
    }>;
    type?: string;
  };
  twitter?: {
    cardType?: string;
    site?: string;
    handle?: string;
  };
  additionalMetaTags?: Array<{
    name?: string;
    property?: string;
    content: string;
  }>;
  noindex?: boolean;
  nofollow?: boolean;
}

const defaultSEO = {
  title: 'EventDiscover - Find Amazing Events',
  description: 'Discover and book tickets for amazing events from top promoters. Find concerts, festivals, conferences, and more in your area.',
  canonical: process.env.NEXT_PUBLIC_SITE_URL || 'https://eventdiscover.com',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: process.env.NEXT_PUBLIC_SITE_URL || 'https://eventdiscover.com',
    siteName: 'EventDiscover',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'EventDiscover - Find Amazing Events',
      },
    ],
  },
  twitter: {
    handle: '@eventdiscover',
    site: '@eventdiscover',
    cardType: 'summary_large_image',
  },
};

export function SEO({
  title,
  description,
  canonical,
  openGraph,
  twitter,
  additionalMetaTags,
  noindex = false,
  nofollow = false,
}: SEOProps) {
  const seoTitle = title ? `${title} | EventDiscover` : defaultSEO.title;
  const seoDescription = description || defaultSEO.description;
  const seoCanonical = canonical || defaultSEO.canonical;

  return (
    <NextSeo
      title={seoTitle}
      description={seoDescription}
      canonical={seoCanonical}
      noindex={noindex}
      nofollow={nofollow}
      openGraph={{
        ...defaultSEO.openGraph,
        title: openGraph?.title || seoTitle,
        description: openGraph?.description || seoDescription,
        url: seoCanonical,
        ...openGraph,
      }}
      twitter={{
        ...defaultSEO.twitter,
        ...twitter,
      }}
      additionalMetaTags={[
        {
          name: 'viewport',
          content: 'width=device-width, initial-scale=1',
        },
        {
          name: 'theme-color',
          content: '#3b82f6',
        },
        {
          name: 'apple-mobile-web-app-capable',
          content: 'yes',
        },
        {
          name: 'apple-mobile-web-app-status-bar-style',
          content: 'default',
        },
        ...(additionalMetaTags || []),
      ]}
    />
  );
}
