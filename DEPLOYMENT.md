# FTP Deployment Guide

This project includes an automated FTP deployment script for cPanel hosting.

## Prerequisites

1. **Node.js environment** on your cPanel hosting
2. **FTP credentials** configured in `.env.ftp`
3. **SSH/Terminal access** to your cPanel (for running npm commands)

## Deployment Commands

### Quick Deploy (Build + Upload)
```bash
npm run deploy:ftp:full
```

### Upload Only (if already built)
```bash
npm run deploy:ftp
```

### Manual Build First
```bash
npm run build
npm run deploy:ftp
```

## What Gets Deployed

The script uploads:
- ✅ Built application (`.next` folder)
- ✅ Public assets (`public` folder)
- ✅ Source code (`pages`, `components`, `styles`, etc.)
- ✅ Package files (`package.json`, `package-lock.json`)

The script excludes:
- ❌ `node_modules` (too large, install on server)
- ❌ `.git` folder
- ❌ Environment files (`.env.*`)
- ❌ Development files

## Post-Deployment Steps on cPanel

After the FTP upload completes, you need to:

1. **SSH into your cPanel** or use the Terminal in cPanel
2. **Navigate to your app directory**:
   ```bash
   cd /public_html/enduserportal
   ```
3. **Install dependencies**:
   ```bash
   npm install --production
   ```
4. **Start the application**:
   ```bash
   npm start
   ```

## Configuration

### FTP Settings (`.env.ftp`)
```
FTP_HOST=ftp.eventway.eu
FTP_USERNAME=<EMAIL>
FTP_PASSWORD=kl9+l-BW4KhRIOdb
FTP_REMOTE_DIR=/public_html/enduserportal
```

### Customizing the Deployment

Edit `deploy-ftp.js` to:
- Add/remove files or folders to upload
- Change exclusion patterns
- Modify upload behavior

## Troubleshooting

### Common Issues:

1. **FTP Connection Failed**
   - Check your FTP credentials in `.env.ftp`
   - Verify FTP server is accessible

2. **Build Failed**
   - Fix any TypeScript/ESLint errors
   - Ensure all dependencies are installed locally

3. **Upload Failed**
   - Check file permissions on remote server
   - Verify remote directory exists and is writable

4. **App Won't Start on Server**
   - Ensure Node.js is enabled on cPanel
   - Check that `npm install` completed successfully
   - Verify the correct Node.js version is selected

### Logs and Debugging

The deployment script provides verbose output showing:
- Build progress
- FTP connection status
- File upload progress
- Any errors encountered

## Security Notes

- Never commit `.env.ftp` to version control
- Use strong FTP passwords
- Consider using SFTP if available
- Regularly rotate FTP credentials
