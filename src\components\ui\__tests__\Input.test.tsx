import { render, screen, fireEvent } from '@testing-library/react';
import { Input } from '../Input';

describe('Input Component', () => {
  it('renders with label', () => {
    render(<Input label="Test Label" />);
    expect(screen.getByLabelText('Test Label')).toBeInTheDocument();
  });

  it('displays error message', () => {
    render(<Input label="Test" error="This field is required" />);
    expect(screen.getByText('This field is required')).toBeInTheDocument();
  });

  it('displays helper text when no error', () => {
    render(<Input label="Test" helperText="Optional field" />);
    expect(screen.getByText('Optional field')).toBeInTheDocument();
  });

  it('handles input changes', () => {
    const handleChange = jest.fn();
    render(<Input label="Test" onChange={handleChange} />);
    
    const input = screen.getByLabelText('Test');
    fireEvent.change(input, { target: { value: 'test value' } });
    
    expect(handleChange).toHaveBeenCalled();
  });

  it('applies custom className', () => {
    render(<Input label="Test" className="custom-class" />);
    const input = screen.getByLabelText('Test');
    expect(input).toHaveClass('custom-class');
  });

  it('shows error styling when error is present', () => {
    render(<Input label="Test" error="Error message" />);
    const input = screen.getByLabelText('Test');
    expect(input).toHaveClass('border-red-500');
  });
});
