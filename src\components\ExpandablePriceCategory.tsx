'use client';

import { useState } from 'react';
import { Minus, Plus, AlertCircle } from 'lucide-react';
import { PriceCategory } from '@/types';
import { Button } from './ui/Button';

interface ExpandablePriceCategoryProps {
  category: PriceCategory;
  isExpanded: boolean;
  onToggle: () => void;
  quantity: number;
  onQuantityChange: (quantity: number) => void;
}

export function ExpandablePriceCategory({
  category,
  isExpanded,
  onToggle,
  quantity,
  onQuantityChange
}: ExpandablePriceCategoryProps) {
  const isExpired = category.availableTickets === 0;
  const availableTickets = typeof category.availableTickets === 'number' && !isNaN(category.availableTickets)
    ? category.availableTickets
    : 1000; // Default fallback
  const maxQuantity = Math.min(availableTickets, 10); // Max 10 tickets per purchase
  
  const handleQuantityChange = (newQuantity: number) => {
    const validQuantity = Math.max(0, Math.min(newQuantity, maxQuantity));
    onQuantityChange(validQuantity);
  };

  const totalPrice = category.price * quantity;

  return (
    <div className="border border-gray-900 overflow-hidden transition-all duration-300 hover:shadow-[0_0_10px_rgba(0,123,255,0.45)]">
      {/* Category Header - Always Visible */}
      <div
        className={`p-2 min-[48rem]:p-3 cursor-pointer transition-all duration-300 ${
          isExpired
            ? 'bg-gray-200 opacity-60'
            : isExpanded
            ? 'bg-gray-100 border-b border-gray-900'
            : 'hover:bg-gray-50 hover:shadow-[0_0_8px_rgba(0,123,255,0.4)]'
        }`}
        onClick={!isExpired ? onToggle : undefined}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h3 className={`font-semibold text-xs min-[48rem]:text-sm ${isExpired ? 'text-gray-500' : 'text-black'}`}>
              {category.name}
            </h3>
            {isExpired && (
              <div className="flex items-center gap-1">
                <AlertCircle className="w-3 h-3 text-black" />
                <span className="text-xs text-black font-bold">SOLD OUT</span>
              </div>
            )}
          </div>

          <div className="text-right">
            {isExpired ? (
              <div className="relative">
                <span className="text-sm min-[48rem]:text-base font-bold text-gray-400">
                  ${category.price.toFixed(2)}
                </span>
                {/* Scratched-off effect */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-full h-0.5 bg-black transform rotate-12"></div>
                </div>
              </div>
            ) : (
              <span className="text-sm min-[48rem]:text-base font-bold text-black">
                ${category.price.toFixed(2)}
              </span>
            )}

            {!isExpired && (
              <div className="text-xs text-gray-600">
                {category.availableTickets} left
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Expanded Content */}
      {isExpanded && !isExpired && (
        <div className="p-2 min-[48rem]:p-3 bg-white border-t border-gray-900">
          <div className="flex items-center justify-between">
            {/* Left side - Category info */}
            <div>
              <h4 className="font-bold text-black text-sm min-[48rem]:text-base">{category.name}</h4>
              <p className="text-black font-bold text-base min-[48rem]:text-lg">
                ${category.price.toFixed(2)}
              </p>
            </div>

            {/* Right side - Quantity selector and total */}
            <div className="flex items-center gap-2 min-[48rem]:gap-3">
              {/* Quantity Selector */}
              <div className="flex items-center gap-1">
                <button
                  onClick={() => handleQuantityChange(quantity - 1)}
                  disabled={quantity <= 0}
                  className="w-6 h-6 min-[48rem]:w-7 min-[48rem]:h-7 border border-gray-900 flex items-center justify-center hover:bg-gray-100 hover:shadow-[0_0_8px_rgba(0,123,255,0.45)] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
                >
                  <Minus className="w-3 h-3" />
                </button>

                <span className="w-6 text-center font-bold text-black text-sm">
                  {quantity}
                </span>

                <button
                  onClick={() => handleQuantityChange(quantity + 1)}
                  disabled={quantity >= maxQuantity}
                  className="w-6 h-6 min-[48rem]:w-7 min-[48rem]:h-7 border border-gray-900 flex items-center justify-center hover:bg-gray-100 hover:shadow-[0_0_8px_rgba(0,123,255,0.45)] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
                >
                  <Plus className="w-3 h-3" />
                </button>
              </div>

              {/* Total Cost Display */}
              {quantity > 0 && (
                <div className="text-right">
                  <div className="text-xs text-gray-700 font-medium">Total</div>
                  <div className="font-bold text-sm min-[48rem]:text-base text-black">
                    ${totalPrice.toFixed(2)}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
