# Portal Settings Application Guide

## 🎯 Overview

This guide explains how to apply portal settings and how each setting affects UI elements in your application. Portal settings provide a comprehensive theming and customization system with three main categories:

- **Global Settings**: Applied across all pages and components
- **Link Page Settings**: Specific to promoter link pages
- **Event Page Settings**: Specific to event detail pages

## 🔧 How Settings Are Applied

### 1. CSS Variables System

Portal settings are applied through CSS custom properties (variables) that are set on the document root:

```typescript
// Settings are converted to CSS variables
const cssVars = {
  '--portal-theme': 'light',
  '--portal-accent-color': '#3b82f6',
  '--portal-border-width': '1px',
  '--portal-background-color': '#ffffff',
  '--portal-background-opacity': '1',
  '--portal-font-family': 'Inter, system-ui, sans-serif',
  '--portal-corner-radius': '0.375rem'
};

// Applied to document root
document.documentElement.style.setProperty(property, value);
```

### 2. Settings Inheritance

The system uses a hierarchical inheritance model:

- **Global Settings**: Base settings inherited by all pages
- **Page-Specific Settings**: Override global settings for specific pages
- **Component Settings**: Can override both global and page settings

## 📋 Global Settings Effects

### Theme (`theme`)
**Values**: `"light"` | `"dark"`
**CSS Variable**: `--portal-theme`

**Effects**:
- Controls overall color scheme
- Affects text colors, background contrasts
- Influences component styling variants
- Used in conditional CSS classes: `theme-${theme}`

```css
.component {
  color: var(--portal-theme) === 'dark' ? #ffffff : #000000;
}
```

### Accent Color (`accentColor`)
**Values**: Hex color (e.g., `"#3b82f6"`)
**CSS Variable**: `--portal-accent-color`

**Effects**:
- Primary color for buttons, links, and interactive elements
- Border colors for focused inputs
- Highlight colors for selected states
- Brand color throughout the interface

```css
.button-primary {
  background-color: var(--portal-accent-color);
}

.input:focus {
  border-color: var(--portal-accent-color);
}
```

### Border Width (`borderWidth`)
**Values**: Number (0-20 pixels)
**CSS Variable**: `--portal-border-width`

**Effects**:
- Thickness of borders on panels, cards, and components
- Input field borders
- Button borders
- Divider line thickness

```css
.panel {
  border: var(--portal-border-width) solid var(--portal-accent-color);
}
```

### Background Color (`backgroundColor`)
**Structure**: `{ color: string, opacity: number }`
**CSS Variables**: `--portal-background-color`, `--portal-background-opacity`

**Effects**:
- Main background color for panels and cards
- Modal and dialog backgrounds
- Content area backgrounds
- Applied with opacity for layering effects

```css
.panel {
  background-color: var(--portal-background-color);
  opacity: var(--portal-background-opacity);
}
```

### Typography (`typography.fontFamily`)
**Values**: CSS font family string
**CSS Variable**: `--portal-font-family`

**Effects**:
- Font family for all text elements
- Affects readability and brand consistency
- Applied to headings, body text, and UI elements

```css
body, .component {
  font-family: var(--portal-font-family);
}
```

### Panel Settings (`panelSettings.cornerStyle`)
**Values**: `"rounded"` | `"square"`
**CSS Variable**: `--portal-corner-radius`

**Effects**:
- Border radius for panels, cards, and containers
- Button corner styling
- Input field corners
- Modal and dialog corners

```css
.panel {
  border-radius: var(--portal-corner-radius);
}

/* Resolves to: */
/* rounded: 0.375rem (6px) */
/* square: 0 */
```

## 🔗 Link Page Settings Effects

### Background Options (`backgroundOptions`)
**Structure**: `{ type: BackgroundType, value: string, makeDefault: boolean }`

#### Background Types and Effects:

**Color Background** (`type: "color"`):
```css
.link-page {
  background-color: #f8fafc; /* value */
}
```

**Gradient Background** (`type: "gradient"`):
```css
.link-page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); /* value */
}
```

**Image Background** (`type: "image"`):
```css
.link-page {
  background-image: url('https://example.com/bg.jpg'); /* value */
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
```

**Video Background** (`type: "video"`):
- Creates HTML5 video element
- Positioned absolutely behind content
- Auto-plays, loops, and is muted
- Covers entire background area

### Link Styling (`linkStyling`)

#### Link Background Color (`linkStyling.backgroundColor`)
**Structure**: `ColorWithOpacity | null`
**Inheritance**: If `null`, inherits from global `backgroundColor`

**Effects**:
- Background color of individual link buttons/cards
- Overrides global panel background for links
- Applied with opacity for visual layering

#### Link Corner Style (`linkStyling.cornerStyle`)
**Values**: `"rounded"` | `"square"` | `null`
**Inheritance**: If `null`, inherits from global `panelSettings.cornerStyle`

**Effects**:
- Corner styling specific to link elements
- Independent of global panel corner settings
- Allows link-specific visual differentiation

### Make Default (`makeDefault`)
**Values**: `boolean`

**Effects**:
- When `true`, applies background to global settings
- Makes the background the default for all new pages
- Propagates setting across the entire portal

## 📅 Event Page Settings Effects

### Background Options (`backgroundOptions`)
Same structure and effects as Link Page backgrounds, but applied specifically to event detail pages.

**Effects**:
- Event page background styling
- Independent of link page backgrounds
- Inherits global settings for other properties

## 🎨 Practical Application Examples

### Using CSS Variables in Components
```css
.custom-component {
  background-color: var(--portal-background-color);
  border: var(--portal-border-width) solid var(--portal-accent-color);
  border-radius: var(--portal-corner-radius);
  font-family: var(--portal-font-family);
  opacity: var(--portal-background-opacity);
}

.custom-button {
  background-color: var(--portal-accent-color);
  border-radius: var(--portal-corner-radius);
  border: var(--portal-border-width) solid transparent;
}
```

### React Component Integration
```typescript
const MyComponent = () => {
  const { resolvedSettings } = usePortalSettingsStore();
  
  const componentStyle = {
    backgroundColor: resolvedSettings?.globalSettings?.backgroundColor?.color,
    borderWidth: `${resolvedSettings?.globalSettings?.borderWidth}px`,
    borderColor: resolvedSettings?.globalSettings?.accentColor,
    fontFamily: resolvedSettings?.globalSettings?.typography?.fontFamily
  };
  
  return (
    <div 
      className={`theme-${resolvedSettings?.globalSettings?.theme}`}
      style={componentStyle}
    >
      Content
    </div>
  );
};
```

### Background Application
```typescript
const getBackgroundStyle = (backgroundOptions) => {
  switch (backgroundOptions.type) {
    case 'color':
      return { backgroundColor: backgroundOptions.value };
    case 'gradient':
      return { background: backgroundOptions.value };
    case 'image':
      return {
        backgroundImage: `url(${backgroundOptions.value})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      };
    case 'video':
      // Requires video element creation
      return { backgroundColor: '#000000' }; // Fallback
  }
};
```

## 🔄 Settings Resolution Process

1. **Load User Settings**: Fetch user-specific portal settings
2. **Apply Inheritance**: Resolve null values with global defaults
3. **Generate CSS Variables**: Convert resolved settings to CSS custom properties
4. **Apply to Document**: Set CSS variables on document root
5. **Component Updates**: Components automatically use updated variables

## 📱 Responsive Considerations

Portal settings are applied globally and work with responsive design:

- CSS variables work across all screen sizes
- Background images scale appropriately
- Typography remains consistent
- Border widths and corner radii adapt to component sizes

## 🎯 Best Practices

1. **Use CSS Variables**: Always reference `var(--portal-*)` in stylesheets
2. **Provide Fallbacks**: Include fallback values for CSS variables
3. **Test Inheritance**: Verify null values inherit correctly
4. **Validate Settings**: Ensure color values and URLs are valid
5. **Performance**: Cache resolved settings to avoid recalculation

## 🛠️ Implementation Patterns

### 1. Settings Store Integration
```typescript
// Access settings in any component
import { usePortalSettingsStore } from '../stores/portalSettingsStore';

const MyComponent = () => {
  const {
    resolvedSettings,
    isLoading,
    updateGlobalSettings,
    updateLinkPageSettings
  } = usePortalSettingsStore();

  // Use settings for styling
  const theme = resolvedSettings?.globalSettings?.theme || 'light';
  const accentColor = resolvedSettings?.globalSettings?.accentColor || '#3b82f6';

  return (
    <div className={`component theme-${theme}`}>
      <button
        style={{ backgroundColor: accentColor }}
        onClick={() => updateGlobalSettings({ theme: 'dark' })}
      >
        Toggle Theme
      </button>
    </div>
  );
};
```

### 2. Custom Hook for Theme Access
```typescript
// Create a custom hook for easy theme access
export const usePortalTheme = () => {
  const { resolvedSettings } = usePortalSettingsStore();

  return {
    theme: resolvedSettings?.globalSettings?.theme || 'light',
    accentColor: resolvedSettings?.globalSettings?.accentColor || '#3b82f6',
    borderWidth: resolvedSettings?.globalSettings?.borderWidth || 1,
    cornerStyle: resolvedSettings?.globalSettings?.panelSettings?.cornerStyle || 'rounded',
    fontFamily: resolvedSettings?.globalSettings?.typography?.fontFamily || 'Inter, system-ui, sans-serif',
    backgroundColor: resolvedSettings?.globalSettings?.backgroundColor || { color: '#ffffff', opacity: 1 }
  };
};

// Usage in components
const MyStyledComponent = () => {
  const { theme, accentColor, cornerStyle } = usePortalTheme();

  return (
    <div className={`styled-component theme-${theme} corner-${cornerStyle}`}>
      <span style={{ color: accentColor }}>Themed content</span>
    </div>
  );
};
```

### 3. Conditional Styling Based on Settings
```typescript
const ConditionalComponent = () => {
  const { resolvedSettings } = usePortalSettingsStore();

  const getConditionalStyles = () => {
    const global = resolvedSettings?.globalSettings;
    if (!global) return {};

    return {
      // Dynamic border based on width setting
      border: global.borderWidth > 0
        ? `${global.borderWidth}px solid ${global.accentColor}`
        : 'none',

      // Conditional corner radius
      borderRadius: global.panelSettings?.cornerStyle === 'rounded'
        ? '0.375rem'
        : '0',

      // Theme-based text color
      color: global.theme === 'dark' ? '#ffffff' : '#000000',

      // Background with opacity
      backgroundColor: `${global.backgroundColor.color}${Math.round(global.backgroundColor.opacity * 255).toString(16).padStart(2, '0')}`
    };
  };

  return (
    <div style={getConditionalStyles()}>
      Conditionally styled content
    </div>
  );
};
```

## 🎨 Advanced Styling Techniques

### 1. CSS-in-JS with Portal Settings
```typescript
import styled from 'styled-components';

const StyledPanel = styled.div`
  background-color: var(--portal-background-color);
  border: var(--portal-border-width) solid var(--portal-accent-color);
  border-radius: var(--portal-corner-radius);
  font-family: var(--portal-font-family);
  opacity: var(--portal-background-opacity);

  /* Theme-specific styles */
  ${props => props.theme === 'dark' && `
    box-shadow: 0 4px 6px rgba(255, 255, 255, 0.1);
  `}

  ${props => props.theme === 'light' && `
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  `}
`;

const MyComponent = () => {
  const { theme } = usePortalTheme();

  return (
    <StyledPanel theme={theme}>
      Styled with CSS-in-JS and portal settings
    </StyledPanel>
  );
};
```

### 2. Tailwind CSS Integration
```typescript
// Create utility classes that use portal settings
const getThemeClasses = (theme: string, cornerStyle: string) => {
  return cn(
    'p-4 border transition-all duration-200',
    theme === 'dark' ? 'bg-gray-800 text-white' : 'bg-white text-gray-900',
    cornerStyle === 'rounded' ? 'rounded-md' : 'rounded-none'
  );
};

const TailwindComponent = () => {
  const { theme, cornerStyle } = usePortalTheme();

  return (
    <div
      className={getThemeClasses(theme, cornerStyle)}
      style={{
        borderColor: 'var(--portal-accent-color)',
        borderWidth: 'var(--portal-border-width)',
        fontFamily: 'var(--portal-font-family)'
      }}
    >
      Tailwind + Portal Settings
    </div>
  );
};
```

### 3. Video Background Implementation
```typescript
const VideoBackground = ({ videoUrl, children }) => {
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.play().catch(console.error);
    }
  }, [videoUrl]);

  return (
    <div className="relative overflow-hidden">
      <video
        ref={videoRef}
        className="absolute inset-0 w-full h-full object-cover -z-10"
        autoPlay
        loop
        muted
        playsInline
        src={videoUrl}
      />
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};

// Usage with portal settings
const PageWithVideoBackground = () => {
  const { resolvedSettings } = usePortalSettingsStore();
  const backgroundOptions = resolvedSettings?.linkPageSettings?.backgroundOptions;

  if (backgroundOptions?.type === 'video') {
    return (
      <VideoBackground videoUrl={backgroundOptions.value}>
        <div>Page content over video background</div>
      </VideoBackground>
    );
  }

  return <div>Regular page content</div>;
};
```

## 🔍 Troubleshooting Common Issues

### 1. Settings Not Applying
**Problem**: Changes to portal settings don't reflect in the UI

**Solutions**:
```typescript
// Check if settings are loaded
const { resolvedSettings, isLoading } = usePortalSettingsStore();

if (isLoading) {
  return <LoadingSpinner />;
}

if (!resolvedSettings) {
  console.warn('Portal settings not loaded');
  // Apply default settings or show error
}

// Verify CSS variables are set
const checkCSSVariables = () => {
  const root = document.documentElement;
  const accentColor = getComputedStyle(root).getPropertyValue('--portal-accent-color');
  console.log('Current accent color:', accentColor);
};
```

### 2. Inheritance Issues
**Problem**: Null values not inheriting from global settings

**Solutions**:
```typescript
// Always provide fallbacks
const getEffectiveValue = (pageValue, globalValue, defaultValue) => {
  return pageValue ?? globalValue ?? defaultValue;
};

// Example usage
const effectiveCornerStyle = getEffectiveValue(
  linkPageSettings?.linkStyling?.cornerStyle,
  globalSettings?.panelSettings?.cornerStyle,
  'rounded'
);
```

### 3. CSS Variable Fallbacks
**Problem**: CSS variables not working in older browsers

**Solutions**:
```css
.component {
  /* Fallback for older browsers */
  background-color: #3b82f6;
  /* Modern browsers with CSS variables */
  background-color: var(--portal-accent-color, #3b82f6);

  border-width: 1px;
  border-width: var(--portal-border-width, 1px);
}
```

### 4. Performance Optimization
**Problem**: Settings causing unnecessary re-renders

**Solutions**:
```typescript
// Memoize computed styles
const memoizedStyles = useMemo(() => {
  if (!resolvedSettings) return {};

  return {
    backgroundColor: resolvedSettings.globalSettings.backgroundColor.color,
    borderColor: resolvedSettings.globalSettings.accentColor,
    borderWidth: `${resolvedSettings.globalSettings.borderWidth}px`
  };
}, [resolvedSettings]);

// Use React.memo for components that only depend on specific settings
const ThemeAwareComponent = React.memo(({ theme, accentColor, children }) => {
  return (
    <div className={`theme-${theme}`} style={{ borderColor: accentColor }}>
      {children}
    </div>
  );
});
```

## 📚 API Integration Examples

### 1. Updating Settings via API
```typescript
// Update global settings
const updateTheme = async (newTheme: 'light' | 'dark') => {
  try {
    const response = await fetch('/api/portal-settings/update/user123/global', {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        theme: newTheme
      })
    });

    if (response.ok) {
      const data = await response.json();
      // Settings store will automatically update and apply changes
      console.log('Theme updated successfully');
    }
  } catch (error) {
    console.error('Failed to update theme:', error);
  }
};

// Update background with makeDefault
const setDefaultBackground = async (backgroundOptions) => {
  const settings = {
    backgroundOptions: {
      ...backgroundOptions,
      makeDefault: true // This will apply to global settings
    }
  };

  await updateLinkPageSettings(settings);
};
```

### 2. Bulk Settings Update
```typescript
const updateAllSettings = async (newSettings) => {
  const { updateAllSettings } = usePortalSettingsStore();

  try {
    await updateAllSettings({
      globalSettings: {
        theme: 'dark',
        accentColor: '#ff6b35',
        borderWidth: 2
      },
      linkPageSettings: {
        backgroundOptions: {
          type: 'gradient',
          value: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          makeDefault: false
        }
      },
      eventPageSettings: {
        backgroundOptions: {
          type: 'image',
          value: 'https://example.com/event-bg.jpg',
          makeDefault: false
        }
      }
    });
  } catch (error) {
    console.error('Failed to update settings:', error);
  }
};
```

This comprehensive guide provides the foundation for implementing and understanding how portal settings affect your application's visual appearance and user experience.
