import Link from 'next/link';
import { ExternalLink, Facebook, Instagram, Twitter, Globe, Users } from 'lucide-react';
import { Promoter } from '@/types';
import { Button } from './ui/Button';

interface PromoterCardProps {
  promoter: Promoter;
}

export function PromoterCard({ promoter }: PromoterCardProps) {
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Event Promoter</h3>

      <div className="flex items-center space-x-4 mb-4">
        <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
          <span className="text-white font-bold text-xl">
            {promoter.name.charAt(0).toUpperCase()}
          </span>
        </div>
        <div>
          <h4 className="font-semibold text-gray-900">{promoter.name}</h4>
          {promoter.description && (
            <p className="text-sm text-gray-600 mb-1 line-clamp-2">
              {promoter.description}
            </p>
          )}
          {promoter.stats && (
            <p className="text-sm text-gray-600">
              {promoter.stats.totalEvents} events organized
            </p>
          )}
        </div>
      </div>

      {/* Social Links */}
      {promoter.socials && (
        <div className="mb-6">
          <h5 className="text-sm font-medium text-gray-700 mb-2">Connect</h5>
          <div className="flex flex-wrap gap-2">
            {promoter.socials.website && (
              <a
                href={promoter.socials.website}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-1 text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded transition-colors"
              >
                <Globe className="w-3 h-3" />
                <span>Website</span>
                <ExternalLink className="w-2 h-2" />
              </a>
            )}
            {promoter.socials.facebook && (
              <a
                href={promoter.socials.facebook}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 px-2 py-1 rounded transition-colors"
              >
                <Facebook className="w-3 h-3" />
                <span>Facebook</span>
                <ExternalLink className="w-2 h-2" />
              </a>
            )}
            {promoter.socials.instagram && (
              <a
                href={promoter.socials.instagram}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-1 text-xs bg-pink-100 hover:bg-pink-200 text-pink-700 px-2 py-1 rounded transition-colors"
              >
                <Instagram className="w-3 h-3" />
                <span>Instagram</span>
                <ExternalLink className="w-2 h-2" />
              </a>
            )}
            {promoter.socials.twitter && (
              <a
                href={promoter.socials.twitter}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 px-2 py-1 rounded transition-colors"
              >
                <Twitter className="w-3 h-3" />
                <span>Twitter</span>
                <ExternalLink className="w-2 h-2" />
              </a>
            )}
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="space-y-3">
        <Link href={`/promoters/${promoter._id}`} className="block">
          <Button variant="outline" className="w-full">
            <Users className="w-4 h-4 mr-2" />
            View All Events
          </Button>
        </Link>
        <Button variant="ghost" className="w-full">
          Follow Promoter
        </Button>
      </div>

      {/* Trust Indicators */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>Verified Promoter</span>
          <span className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Active</span>
          </span>
        </div>
      </div>
    </div>
  );
}
