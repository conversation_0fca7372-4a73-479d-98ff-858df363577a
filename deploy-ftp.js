const ftp = require('basic-ftp');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
require('dotenv').config({ path: '.env.ftp' });

class FTPDeployer {
    constructor() {
        this.client = new ftp.Client();
        this.client.ftp.verbose = true; // Enable verbose logging
        
        this.config = {
            host: process.env.FTP_HOST,
            user: process.env.FTP_USERNAME,
            password: process.env.FTP_PASSWORD,
            remoteDir: process.env.FTP_REMOTE_DIR || '/public_html'
        };
        
        // Files and folders to exclude from upload
        this.excludePatterns = [
            'node_modules',
            '.git',
            '.env.ftp',
            '.env.local',
            '.env.development',
            'deploy-ftp.js',
            'README.md',
            '.gitignore',
            '.eslintrc.json',
            'next.config.js.backup'
        ];
    }

    async connect() {
        console.log('🔌 Connecting to FTP server...');
        try {
            await this.client.access(this.config);
            console.log('✅ Connected to FTP server successfully');
        } catch (error) {
            console.error('❌ Failed to connect to FTP server:', error.message);
            throw error;
        }
    }

    async buildProject() {
        console.log('🏗️  Building Next.js project...');
        try {
            execSync('npm run build', { stdio: 'inherit' });
            console.log('✅ Build completed successfully');
        } catch (error) {
            console.error('❌ Build failed:', error.message);
            throw error;
        }
    }

    shouldExclude(filePath) {
        return this.excludePatterns.some(pattern => 
            filePath.includes(pattern) || filePath.endsWith(pattern)
        );
    }

    async uploadDirectory(localDir, remoteDir) {
        console.log(`📁 Uploading directory: ${localDir} -> ${remoteDir}`);

        try {
            // Normalize and clean the remote path
            const cleanRemoteDir = remoteDir.replace(/\/+/g, '/').replace(/\/$/, '');

            // Ensure remote directory exists
            await this.ensureRemoteDir(cleanRemoteDir);

            const items = fs.readdirSync(localDir);

            for (const item of items) {
                const localPath = path.join(localDir, item);
                const remotePath = `${cleanRemoteDir}/${item}`.replace(/\/+/g, '/');

                if (this.shouldExclude(localPath)) {
                    console.log(`⏭️  Skipping: ${localPath}`);
                    continue;
                }

                const stat = fs.statSync(localPath);

                if (stat.isDirectory()) {
                    await this.uploadDirectory(localPath, remotePath);
                } else {
                    console.log(`📄 Uploading file: ${localPath} -> ${remotePath}`);
                    await this.uploadFile(localPath, remotePath);
                }
            }
        } catch (error) {
            console.error(`❌ Error uploading directory ${localDir}:`, error.message);
            throw error;
        }
    }

    async ensureRemoteDir(remotePath) {
        try {
            // Split path and create directories step by step
            const pathParts = remotePath.split('/').filter(part => part.length > 0);
            let currentPath = '';

            for (const part of pathParts) {
                currentPath += '/' + part;
                try {
                    await this.client.ensureDir(currentPath);
                } catch (error) {
                    // Directory might already exist, continue
                    console.log(`⚠️  Could not create directory ${currentPath}: ${error.message}`);
                }
            }
        } catch (error) {
            console.log(`⚠️  Error ensuring directory ${remotePath}: ${error.message}`);
        }
    }

    async uploadFile(localPath, remotePath) {
        console.log(`📄 Uploading file: ${localPath} -> ${remotePath}`);
        try {
            // Ensure the parent directory exists
            const parentDir = path.dirname(remotePath).replace(/\\/g, '/');
            if (parentDir !== '.' && parentDir !== '/') {
                await this.ensureRemoteDir(parentDir);
            }

            // Clean the remote path
            const cleanRemotePath = remotePath.replace(/\/+/g, '/');

            await this.client.uploadFrom(localPath, cleanRemotePath);
            console.log(`✅ Uploaded: ${cleanRemotePath}`);
        } catch (error) {
            console.error(`❌ Failed to upload ${localPath}:`, error.message);
            // Don't throw error for individual file failures, continue with other files
            console.log(`⏭️  Continuing with next file...`);
        }
    }

    async clearRemoteDirectory() {
        console.log('🧹 Clearing remote directory...');
        try {
            await this.client.cd(this.config.remoteDir);
            await this.client.clearWorkingDir();
            console.log('✅ Remote directory cleared');
        } catch (error) {
            console.log('⚠️  Could not clear remote directory (might not exist yet)');
        }
    }

    async deploy() {
        try {
            console.log('🚀 Starting deployment process...');
            console.log(`📍 Target: ${this.config.host}${this.config.remoteDir}`);
            
            // Step 1: Build the project
            await this.buildProject();
            
            // Step 2: Connect to FTP
            await this.connect();
            
            // Step 3: Clear remote directory (optional - comment out if you want to keep existing files)
            // await this.clearRemoteDirectory();
            
            // Step 4: Upload essential files
            const filesToUpload = [
                'package.json',
                'package-lock.json',
                'server.js',
                '.npmrc'
            ];

            for (const file of filesToUpload) {
                if (fs.existsSync(file)) {
                    const remotePath = `${this.config.remoteDir}/${file}`.replace(/\/+/g, '/');
                    await this.uploadFile(file, remotePath);
                }
            }
            
            // Step 5: Upload directories
            const dirsToUpload = [
                '.next',
                'public',
                'pages',
                'components',
                'styles',
                'lib',
                'utils'
            ];

            for (const dir of dirsToUpload) {
                if (fs.existsSync(dir)) {
                    const remotePath = `${this.config.remoteDir}/${dir}`.replace(/\/+/g, '/');
                    console.log(`🚀 Starting upload of ${dir} directory...`);
                    await this.uploadDirectory(dir, remotePath);
                    console.log(`✅ Completed upload of ${dir} directory`);
                }
            }
            
            console.log('🎉 Deployment completed successfully!');
            console.log('📝 Next steps on cPanel:');
            console.log('   1. Install dependencies: npm install --production');
            console.log('   2. Start the application: npm start');
            
        } catch (error) {
            console.error('💥 Deployment failed:', error.message);
            process.exit(1);
        } finally {
            this.client.close();
        }
    }
}

// Run deployment
const deployer = new FTPDeployer();
deployer.deploy();
