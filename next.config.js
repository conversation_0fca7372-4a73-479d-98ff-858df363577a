/** @type {import('next').NextConfig} */
const nextConfig = {
  // Disable ESLint and TypeScript checking during build for deployment
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },

  // Enable experimental features
  experimental: {
    // optimizeCss: true, // Disabled due to critters module issue
  },

  // Image optimization
  images: {
    domains: ['localhost', 'eventdiscover.com', 'db.eventway.eu'],
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // Compression
  compress: true,

  // Note: Headers and redirects are disabled for static export
  // They will be handled by the .htaccess file instead
};

module.exports = nextConfig;
