'use client';

import { useState } from 'react';
import { ChevronDown } from 'lucide-react';

interface Language {
  code: string;
  name: string;
  flag: string;
}

const languages: Language[] = [
  {
    code: 'en',
    name: 'English',
    flag: '🇺🇸'
  },
  {
    code: 'bg',
    name: 'Български',
    flag: '🇧🇬'
  }
];

export function LanguageSelector() {
  const [selectedLanguage, setSelectedLanguage] = useState<Language>(languages[0]);
  const [isOpen, setIsOpen] = useState(false);

  const handleLanguageSelect = (language: Language) => {
    setSelectedLanguage(language);
    setIsOpen(false);
    // Here you would typically trigger a language change in your app
    console.log('Language changed to:', language.code);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-1 px-2 py-1 text-xs text-gray-700 hover:text-black hover:shadow-[0_0_8px_rgba(0,123,255,0.45)] transition-all duration-300 border border-gray-900"
      >
        <span className="text-sm">{selectedLanguage.flag}</span>
        <span className="hidden sm:inline text-xs font-medium">{selectedLanguage.name}</span>
        <ChevronDown className={`w-3 h-3 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute bottom-full right-0 mb-1 bg-white border border-gray-900 shadow-lg z-20 min-w-[90px]">
            {languages.map((language) => (
              <button
                key={language.code}
                onClick={() => handleLanguageSelect(language)}
                className={`w-full flex items-center gap-1 px-2 py-1 text-xs hover:bg-gray-100 hover:shadow-[0_0_6px_rgba(0,123,255,0.4)] transition-all duration-300 ${
                  selectedLanguage.code === language.code
                    ? 'bg-gray-100 text-black font-medium'
                    : 'text-gray-700'
                }`}
              >
                <span className="text-sm">{language.flag}</span>
                <span className="text-xs">{language.name}</span>
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  );
}
