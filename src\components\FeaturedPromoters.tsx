'use client';

import Link from 'next/link';
import { Promoter } from '@/types';
import { Button } from './ui/Button';
import { LoadingSpinner } from './ui/LoadingSpinner';
import { usePromoters } from '@/hooks/usePromoters';

interface FeaturedPromotersProps {
  promoters?: Promoter[];
}

export function FeaturedPromoters({ promoters: initialPromoters }: FeaturedPromotersProps) {
  // Use React Query to fetch real data from API
  const { data, isLoading, error } = usePromoters({ limit: 6 });

  // Get promoters from API
  const promoters = data?.data?.promoters || [];

  if (isLoading) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <LoadingSpinner size="lg" className="text-primary-500" />
            <p className="mt-4 text-gray-600">Loading featured promoters...</p>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p className="text-red-600">Failed to load promoters. Please try again later.</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="font-display text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Featured Promoters
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover events from the most trusted and popular promoters in the industry
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {promoters.map((promoter) => (
            <Link
              key={promoter._id}
              href={`/promoters/${promoter._id}`}
              className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group"
            >
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-xl">
                    {promoter.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <h3 className="font-semibold text-lg text-gray-900 group-hover:text-primary-600 transition-colors">
                    {promoter.name}
                  </h3>
                  {promoter.description && (
                    <p className="text-gray-600 text-sm mb-1 line-clamp-2">
                      {promoter.description}
                    </p>
                  )}
                  {promoter.stats && (
                    <p className="text-gray-600 text-sm">
                      {promoter.stats.upcomingEvents} upcoming events
                    </p>
                  )}
                </div>
              </div>

              {/* Social Links */}
              {promoter.socials && (
                <div className="flex space-x-3 text-gray-400">
                  {promoter.socials.website && (
                    <span className="text-xs">Website</span>
                  )}
                  {promoter.socials.facebook && (
                    <span className="text-xs">Facebook</span>
                  )}
                  {promoter.socials.instagram && (
                    <span className="text-xs">Instagram</span>
                  )}
                  {promoter.socials.twitter && (
                    <span className="text-xs">Twitter</span>
                  )}
                </div>
              )}

              <div className="mt-4 text-primary-600 group-hover:text-primary-700 font-medium text-sm">
                View Events →
              </div>
            </Link>
          ))}
        </div>

        <div className="text-center">
          <Button variant="outline" size="lg">
            <Link href="/promoters">View All Promoters</Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
