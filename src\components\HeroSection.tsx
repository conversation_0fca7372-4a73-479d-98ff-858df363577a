'use client';

import { useState } from 'react';
import { MapPin, Calendar } from 'lucide-react';
import { Button } from './ui/Button';

export function HeroSection() {
  const [location, setLocation] = useState('');
  const [category, setCategory] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const params = new URLSearchParams();
    if (location) params.set('city', location);
    if (category) params.set('category', category);

    window.location.href = `/promoters?${params.toString()}`;
  };

  return (
    <section className="relative bg-gradient-to-br from-primary-600 to-primary-700 text-white">
      <div className="absolute inset-0 bg-black/20"></div>
      <div className="relative container mx-auto px-4 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="font-display text-4xl md:text-6xl font-bold mb-6">
            Discover Amazing Events
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-primary-100">
            Find concerts, festivals, conferences, and more from top promoters in your area
          </p>

          {/* Browse Form */}
          <form onSubmit={handleSearch} className="bg-white rounded-lg p-6 shadow-xl">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Location Input */}
              <div>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="City"
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent outline-none text-gray-900"
                  />
                </div>
              </div>

              {/* Category Select */}
              <div>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <select
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent outline-none text-gray-900 appearance-none"
                  >
                    <option value="">All Categories</option>
                    <option value="music">Music</option>
                    <option value="sports">Sports</option>
                    <option value="theater">Theater</option>
                    <option value="comedy">Comedy</option>
                    <option value="conference">Conference</option>
                    <option value="workshop">Workshop</option>
                  </select>
                </div>
              </div>

              {/* Browse Button */}
              <div>
                <Button type="submit" size="lg" className="w-full px-8">
                  Browse Promoters
                </Button>
              </div>
            </div>
          </form>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">10,000+</div>
              <div className="text-primary-200">Events Listed</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">500+</div>
              <div className="text-primary-200">Trusted Promoters</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">1M+</div>
              <div className="text-primary-200">Tickets Sold</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
