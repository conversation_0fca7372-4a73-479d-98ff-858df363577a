import { ExternalLink, Facebook, Instagram, Twitter, Globe, Calendar, Users } from 'lucide-react';
import { Promoter } from '@/types';
import { Button } from './ui/Button';

interface PromoterHeaderProps {
  promoter: Promoter;
}

export function PromoterHeader({ promoter }: PromoterHeaderProps) {
  return (
    <section className="bg-gradient-to-br from-primary-600 to-primary-700 text-white">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <div className="flex flex-col md:flex-row items-start md:items-center gap-8">
            {/* Promoter Avatar */}
            <div className="w-32 h-32 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-4xl font-bold">
                {promoter.name.charAt(0).toUpperCase()}
              </span>
            </div>

            {/* Promoter Info */}
            <div className="flex-1">
              <h1 className="font-display text-4xl md:text-5xl font-bold mb-4">
                {promoter.name}
              </h1>
              
              {/* Stats */}
              {promoter.stats && (
                <div className="flex flex-wrap gap-6 mb-6">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-5 h-5" />
                    <span>{promoter.stats.totalEvents} Total Events</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Users className="w-5 h-5" />
                    <span>{promoter.stats.upcomingEvents} Upcoming Events</span>
                  </div>
                </div>
              )}

              {/* Social Links */}
              {promoter.socials && (
                <div className="flex flex-wrap gap-4 mb-6">
                  {promoter.socials.website && (
                    <a
                      href={promoter.socials.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center space-x-2 bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors"
                    >
                      <Globe className="w-4 h-4" />
                      <span>Website</span>
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  )}
                  {promoter.socials.facebook && (
                    <a
                      href={promoter.socials.facebook}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center space-x-2 bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors"
                    >
                      <Facebook className="w-4 h-4" />
                      <span>Facebook</span>
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  )}
                  {promoter.socials.instagram && (
                    <a
                      href={promoter.socials.instagram}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center space-x-2 bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors"
                    >
                      <Instagram className="w-4 h-4" />
                      <span>Instagram</span>
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  )}
                  {promoter.socials.twitter && (
                    <a
                      href={promoter.socials.twitter}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center space-x-2 bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors"
                    >
                      <Twitter className="w-4 h-4" />
                      <span>Twitter</span>
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  )}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-4">
                <Button variant="secondary" size="lg">
                  Follow Promoter
                </Button>
                <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-primary-600">
                  Share Profile
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
