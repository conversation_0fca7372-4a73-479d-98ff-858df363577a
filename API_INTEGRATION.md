# API Integration Guide

This document explains how the frontend integrates with your backend API endpoints.

## 🔌 API Configuration

The frontend is configured to connect to your backend API at `http://localhost:3000` by default.

### Environment Variables

Update `.env.local` with your backend URL:

```env
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_SITE_URL=http://localhost:3001
```

## 📡 API Endpoints Integration

The frontend is already configured to use your exact API endpoints:

### Promoters API

- **GET /api/public/promoters** - Get list of public promoters
  - ✅ Supports pagination (`page`, `limit`)
  - ✅ Supports search (`search` parameter)
  - ✅ Returns promoter stats (total events, upcoming events)
  - Used in: `/promoters` page, homepage featured promoters

- **GET /api/public/promoters/:promoterId** - Get single promoter profile
  - ✅ Returns promoter details and statistics
  - ✅ Includes recent events preview
  - Used in: `/promoters/[promoterId]` page

- **GET /api/public/promoters/:promoterId/events** - Get promoter's events
  - ✅ Supports filtering by status (upcoming/past)
  - ✅ Supports category filtering
  - ✅ Supports search
  - ✅ Includes pagination and sorting
  - Used in: Promoter profile pages

### Events API (Expected)

The frontend also expects these event endpoints:

- **GET /api/public/events** - Get list of public events
- **GET /api/public/events/:eventId** - Get single event details
- **GET /api/public/events/:eventId/categories/available** - Get available price categories

## 🔄 Data Flow

### 1. React Query Integration

The frontend uses TanStack Query (React Query) for:
- ✅ Automatic caching
- ✅ Background refetching
- ✅ Error handling
- ✅ Loading states

### 2. Error Handling Strategy

If your backend is not available, the frontend will:
- ✅ Display appropriate error messages
- ✅ Display API status indicator
- ✅ Gracefully handle errors

### 3. Real-time Updates

When your backend is connected:
- ✅ Data refreshes automatically
- ✅ Search and filters work in real-time
- ✅ Pagination updates dynamically

## 🚀 Testing the Integration

### 1. Start Your Backend

Make sure your backend is running on `http://localhost:3000`

### 2. Check API Status

The frontend includes an API status indicator in the top-right corner:
- 🟡 **Checking** - Testing connection
- 🟢 **Connected** - Backend API available
- 🔴 **Disconnected** - Using mock data

### 3. Test Endpoints

Visit these pages to test the integration:

- **Homepage** (`/`) - Tests promoters and events endpoints
- **Promoters** (`/promoters`) - Tests promoters listing
- **Promoter Profile** (`/promoters/1`) - Tests single promoter and their events
- **Event Detail** (`/events/1`) - Tests single event details

## 📊 Expected Data Format

The frontend expects your API to return data in this format:

### Promoter Response
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "_id": "string",
        "name": "string",
        "socials": {
          "website": "string",
          "facebook": "string",
          "instagram": "string",
          "twitter": "string"
        },
        "createdAt": "string",
        "stats": {
          "totalEvents": "number",
          "upcomingEvents": "number"
        }
      }
    ],
    "pagination": {
      "currentPage": "number",
      "totalPages": "number",
      "totalItems": "number"
    }
  }
}
```

### Event Response
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "_id": "string",
        "title": "string",
        "description": "string",
        "dateTime": "string",
        "location": {
          "venue": "string",
          "address": "string",
          "city": "string",
          "coordinates": [number, number]
        },
        "image": {
          "url": "string",
          "alt": "string"
        },
        "promoterId": {
          "_id": "string",
          "name": "string"
        },
        "priceCategories": [
          {
            "_id": "string",
            "name": "string",
            "price": "number",
            "totalTickets": "number",
            "soldTickets": "number",
            "availableTickets": "number",
            "description": "string"
          }
        ],
        "category": "string",
        "tags": ["string"],
        "capacity": "number",
        "ageRestriction": "number",
        "salesActive": "boolean",
        "availableTickets": "number"
      }
    ],
    "pagination": {
      "currentPage": "number",
      "totalPages": "number", 
      "totalItems": "number"
    }
  }
}
```

## 🛠️ Troubleshooting

### Backend Not Connecting?

1. Check if your backend is running on port 3000
2. Verify CORS is enabled for `http://localhost:3001`
3. Check the API status indicator in the top-right corner
4. Open browser dev tools to see network requests

### Data Not Displaying?

1. Check browser console for errors
2. Verify your API returns data in the expected format
3. Test API endpoints directly with curl or Postman

### Need to Modify API Endpoints?

Update the service functions in `src/services/eventService.ts` to match your exact endpoint structure.

## ✅ Ready to Go!

Your frontend is now fully configured to work with your backend API. Simply start your backend server and the integration will work automatically!
