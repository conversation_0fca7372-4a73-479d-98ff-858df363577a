'use client';

import { useEffect, useState } from 'react';
import { CheckCircle, XCircle, Clock } from 'lucide-react';
import { api } from '@/lib/api';

export function ApiStatus() {
  const [status, setStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');

  useEffect(() => {
    const checkApiStatus = async () => {
      try {
        await api.get('/api/public/promoters?limit=1');
        setStatus('connected');
      } catch (error) {
        setStatus('disconnected');
      }
    };

    checkApiStatus();
  }, []);

  const statusConfig = {
    checking: {
      icon: Clock,
      color: 'text-yellow-600',
      bg: 'bg-yellow-50',
      border: 'border-yellow-200',
      text: 'Checking API connection...'
    },
    connected: {
      icon: CheckCircle,
      color: 'text-green-600',
      bg: 'bg-green-50',
      border: 'border-green-200',
      text: 'Connected to backend API'
    },
    disconnected: {
      icon: XCircle,
      color: 'text-red-600',
      bg: 'bg-red-50',
      border: 'border-red-200',
      text: 'Backend API not available'
    }
  };

  const config = statusConfig[status];
  const Icon = config.icon;

  return (
    <div className={`fixed top-4 right-4 z-50 p-3 rounded-lg border ${config.bg} ${config.border} shadow-lg`}>
      <div className="flex items-center space-x-2">
        <Icon className={`w-4 h-4 ${config.color}`} />
        <span className={`text-sm font-medium ${config.color}`}>
          {config.text}
        </span>
      </div>
    </div>
  );
}
