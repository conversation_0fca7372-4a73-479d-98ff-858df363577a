import axios from 'axios';
import {
  getPublicEvents,
  getPublicEvent,
  getPublicPromoters,
  getPublicPromoter
} from '../eventService';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock the api instance
jest.mock('@/lib/api', () => ({
  api: {
    get: jest.fn(),
    post: jest.fn(),
    interceptors: {
      response: {
        use: jest.fn(),
      },
    },
  },
}));

import { api } from '@/lib/api';
const mockApi = api as jest.Mocked<typeof api>;

describe('Event Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getPublicEvents', () => {
    it('fetches public events successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            data: [
              {
                _id: '1',
                title: 'Test Event',
                dateTime: '2024-12-25T19:00:00Z',
              },
            ],
            pagination: {
              currentPage: 1,
              totalPages: 1,
              totalItems: 1,
            },
          },
        },
      };

      mockApi.get.mockResolvedValue(mockResponse);

      const result = await getPublicEvents({ page: 1, limit: 10 });

      expect(mockApi.get).toHaveBeenCalledWith('/events/public', {
        params: { page: 1, limit: 10 },
      });
      expect(result).toEqual(mockResponse.data);
    });

    it('handles API errors', async () => {
      const error = new Error('Network error');
      mockApi.get.mockRejectedValue(error);

      await expect(getPublicEvents()).rejects.toThrow('Network error');
    });
  });

  describe('getPublicEvent', () => {
    it('fetches a single event successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            _id: '1',
            title: 'Test Event',
            dateTime: '2024-12-25T19:00:00Z',
          },
        },
      };

      mockApi.get.mockResolvedValue(mockResponse);

      const result = await getPublicEvent('1');

      expect(mockApi.get).toHaveBeenCalledWith('/events/public/1');
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('getPublicPromoters', () => {
    it('fetches public promoters successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            data: [
              {
                _id: '1',
                name: 'Test Promoter',
                socials: {},
                createdAt: '2024-01-01T00:00:00Z',
              },
            ],
            pagination: {
              currentPage: 1,
              totalPages: 1,
              totalItems: 1,
            },
          },
        },
      };

      mockApi.get.mockResolvedValue(mockResponse);

      const result = await getPublicPromoters({ page: 1, limit: 12 });

      expect(mockApi.get).toHaveBeenCalledWith('/promoters/public', {
        params: { page: 1, limit: 12 },
      });
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('getPublicPromoter', () => {
    it('fetches a single promoter successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            _id: '1',
            name: 'Test Promoter',
            socials: {},
            createdAt: '2024-01-01T00:00:00Z',
            stats: {
              totalEvents: 5,
              upcomingEvents: 2,
            },
          },
        },
      };

      mockApi.get.mockResolvedValue(mockResponse);

      const result = await getPublicPromoter('1');

      expect(mockApi.get).toHaveBeenCalledWith('/promoters/public/1');
      expect(result).toEqual(mockResponse.data);
    });
  });
});
