import Link from 'next/link';
import { 
  Music, 
  Trophy, 
  Theater, 
  Laugh, 
  Users, 
  GraduationCap,
  Palette,
  Utensils 
} from 'lucide-react';

interface EventCategoriesProps {
  categories?: string[];
}

const categoryIcons = {
  music: Music,
  sports: Trophy,
  theater: Theater,
  comedy: Laugh,
  conference: Users,
  workshop: GraduationCap,
  art: Palette,
  food: Utensils,
};

const categoryColors = {
  music: 'from-purple-500 to-pink-500',
  sports: 'from-green-500 to-blue-500',
  theater: 'from-red-500 to-orange-500',
  comedy: 'from-yellow-500 to-orange-500',
  conference: 'from-blue-500 to-indigo-500',
  workshop: 'from-indigo-500 to-purple-500',
  art: 'from-pink-500 to-rose-500',
  food: 'from-orange-500 to-red-500',
};

const defaultCategories = [
  { name: 'music', label: 'Music & Concerts' },
  { name: 'sports', label: 'Sports & Fitness' },
  { name: 'theater', label: 'Theater & Arts' },
  { name: 'comedy', label: 'Comedy Shows' },
  { name: 'conference', label: 'Conferences' },
  { name: 'workshop', label: 'Workshops' },
  { name: 'art', label: 'Art & Culture' },
  { name: 'food', label: 'Food & Drink' },
];

export function EventCategories({ categories }: EventCategoriesProps) {
  const displayCategories = categories 
    ? categories.map(cat => ({ name: cat, label: cat.charAt(0).toUpperCase() + cat.slice(1) }))
    : defaultCategories;

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="font-display text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Browse by Category
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Find exactly what you're looking for with our organized event categories
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {displayCategories.map((category) => {
            const IconComponent = categoryIcons[category.name as keyof typeof categoryIcons] || Users;
            const colorClass = categoryColors[category.name as keyof typeof categoryColors] || 'from-gray-500 to-gray-600';

            return (
              <Link
                key={category.name}
                href={`/promoters?category=${category.name}`}
                className="group"
              >
                <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 text-center">
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br ${colorClass} flex items-center justify-center group-hover:scale-110 transition-transform`}>
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
                    {category.label}
                  </h3>
                </div>
              </Link>
            );
          })}
        </div>

        {/* Call to Action */}
        <div className="mt-16 text-center">
          <div className="bg-white rounded-lg shadow-md p-8 max-w-2xl mx-auto">
            <h3 className="font-display text-2xl font-bold text-gray-900 mb-4">
              Can't find what you're looking for?
            </h3>
            <p className="text-gray-600 mb-6">
              Browse all promoters to discover amazing events from trusted organizers.
            </p>
            <div className="flex justify-center">
              <Link
                href="/promoters"
                className="bg-primary-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-primary-600 transition-colors"
              >
                Browse All Promoters
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
