'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { PaymentMethodSelector, PaymentMethod } from '@/components/PaymentMethodSelector';
import { DateContainer } from '@/components/DateContainer';
import { LanguageSelector } from '@/components/LanguageSelector';
import { usePortalTheme } from '@/hooks/usePortalSettings';
import { applyCSSVariables, getBackgroundStyle, getThemeClasses } from '@/lib/portalSettings';
import { getPayment } from '@/services/eventService';
import { CustomerInfo, CardDetails, PaymentData } from '@/types';

interface PaymentApiData {
  _id: string;
  eventId: {
    _id: string;
    title: string;
    dateTime: string;
    location: {
      venue: string;
      address: string;
      city: string;
      country: string;
    };
    promoterId?: {
      _id: string;
      id?: string;
    } | string;
    status: string;
    availableTickets: number | null;
    salesActive: boolean;
    eventStatus: string;
    daysUntilEvent: number;
    id: string;
  };
  selectedCategories: Array<{
    categoryId: {
      _id: string;
      name: string;
      price: number;
      description: string;
      soldPercentage: number;
      priceInDollars: number;
      formattedPrice: string;
      salesStatus: string;
      id: string;
    };
    quantity: number;
    price: number;
    subtotal: number;
    _id: string;
    id: string;
  }>;
  totalAmount: number;
  status: string;
  currency: string;
  paymentMethod: string;
  discountAmount: number;
  discountPercentage: number;
  createdAt: string;
  updatedAt: string;
  paymentReference: string;
  fullName: string;
  formattedTotalAmount: string;
  totalTickets: number;
  statusDisplay: string;
  isPaid: boolean;
  id: string;
}

export default function CheckoutPage() {
  const params = useParams();
  const router = useRouter();
  const eventId = params.eventId as string;

  // State management
  const [paymentData, setPaymentData] = useState<PaymentApiData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    firstName: '',
    lastName: '',
    email: '',
    phone: ''
  });
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [cardDetails, setCardDetails] = useState<CardDetails>({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: ''
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get promoter ID from payment data to fetch portal settings
  const promoterId = typeof paymentData?.eventId?.promoterId === 'object'
    ? paymentData?.eventId?.promoterId?._id || paymentData?.eventId?.promoterId?.id
    : paymentData?.eventId?.promoterId;

  // Fetch portal settings for the event's promoter
  const {
    resolvedSettings,
    isLoading: settingsLoading,
    theme,
    accentColor,
    borderWidth,
    cornerStyle,
    fontFamily,
    backgroundColor,
    eventPageSettings,
  } = usePortalTheme(promoterId || '');

  // Apply CSS variables when settings are loaded
  useEffect(() => {
    if (resolvedSettings) {
      applyCSSVariables(resolvedSettings);
    }
  }, [resolvedSettings]);

  // Load payment data from API
  useEffect(() => {
    const loadPaymentData = async () => {
      const paymentId = sessionStorage.getItem('paymentId');
      if (!paymentId) {
        // Redirect back to payment page if no payment ID
        router.push(`/events/${eventId}/payment`);
        return;
      }

      try {
        setIsLoading(true);
        const response = await getPayment(paymentId);
        if (response.success) {
          setPaymentData(response.data);
        } else {
          throw new Error(response.message || 'Failed to load payment data');
        }
      } catch (error) {
        console.error('Failed to load payment data:', error);
        router.push(`/events/${eventId}/payment`);
      } finally {
        setIsLoading(false);
      }
    };

    loadPaymentData();
  }, [eventId, router]);

  // Validation
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!customerInfo.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }
    if (!customerInfo.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }
    if (!customerInfo.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(customerInfo.email)) {
      newErrors.email = 'Please enter a valid email';
    }
    if (!selectedPaymentMethod) {
      newErrors.paymentMethod = 'Please select a payment method';
    }

    // Validate card details for Stripe
    if (selectedPaymentMethod === 'stripe') {
      if (!cardDetails.cardholderName.trim()) {
        newErrors.cardholderName = 'Cardholder name is required';
      }
      if (!cardDetails.cardNumber.trim()) {
        newErrors.cardNumber = 'Card number is required';
      }
      if (!cardDetails.expiryDate.trim()) {
        newErrors.expiryDate = 'Expiry date is required';
      }
      if (!cardDetails.cvv.trim()) {
        newErrors.cvv = 'CVV is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handlePlaceOrder = async () => {
    if (!validateForm() || !paymentData) return;

    setIsProcessing(true);
    try {
      // Since payment was already initiated on the previous page,
      // here we would typically complete the payment with customer info
      // For now, we'll simulate the completion process

      // In a real implementation, you might send an update to the payment
      // with customer information and payment method details
      const completionData = {
        paymentId: paymentData._id, // Payment ID from API
        customerInfo,
        paymentMethod: selectedPaymentMethod!,
        cardDetails: selectedPaymentMethod === 'stripe' ? cardDetails : undefined,
      };

      // Simulate payment completion
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Clear payment ID
      sessionStorage.removeItem('paymentId');

      // Show success message and redirect
      alert('Order placed successfully!');
      router.push('/');

    } catch (error: unknown) {
      console.error('Payment completion error:', error);
      alert('Failed to complete payment. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle customer info changes
  const handleCustomerInfoChange = (field: keyof CustomerInfo, value: string) => {
    setCustomerInfo(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Calculate ticket summary
  const getTicketSummary = () => {
    if (!paymentData) return [];

    return paymentData.selectedCategories.map(selection => {
      return {
        name: selection.categoryId.name,
        quantity: selection.quantity,
        price: selection.categoryId.price,
        total: selection.subtotal
      };
    });
  };

  if (isLoading || settingsLoading || !paymentData) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <LoadingSpinner size="lg" className="text-white mb-4" />
          <p>Loading payment data...</p>
        </div>
      </div>
    );
  }

  // Fallback portal settings for demonstration if API fails
  const fallbackSettings = {
    theme: 'light',
    accentColor: '#f43f5e', // Rose
    borderWidth: 1,
    cornerStyle: 'square',
    fontFamily: 'Arial, sans-serif',
    backgroundColor: { color: '#06b6d4', opacity: 1 }, // Cyan
    eventPageSettings: {
      backgroundOptions: { type: 'color', value: '#3b82f6' }, // Blue
      panelSettings: {
        backgroundColor: { color: '#06b6d4', opacity: 1 }, // Cyan
        cornerStyle: 'square'
      }
    }
  };

  // Use fallback if portal settings are not loaded
  const effectiveTheme = theme || fallbackSettings.theme;
  const effectiveAccentColor = accentColor || fallbackSettings.accentColor;
  const effectiveBorderWidth = borderWidth || fallbackSettings.borderWidth;
  const effectiveCornerStyle = cornerStyle || fallbackSettings.cornerStyle;
  const effectiveFontFamily = fontFamily || fallbackSettings.fontFamily;
  const effectiveBackgroundColor = backgroundColor || fallbackSettings.backgroundColor;
  const effectiveEventPageSettings = eventPageSettings || fallbackSettings.eventPageSettings;

  // Get background style from event page settings
  const pageBackgroundStyle = effectiveEventPageSettings?.backgroundOptions
    ? getBackgroundStyle(effectiveEventPageSettings.backgroundOptions)
    : {};

  const ticketSummary = getTicketSummary();

  return (
    <div
      className={`min-h-screen flex items-center justify-center p-2 ${getThemeClasses(effectiveTheme, effectiveCornerStyle)}`}
      style={{
        ...pageBackgroundStyle,
        fontFamily: effectiveFontFamily, // Typography applies to whole page
      }}
    >
      <div
        className="w-full md:w-2/5 min-[48rem]:w-[28%] max-w-[800px] max-h-[95vh] mx-auto shadow-2xl overflow-hidden flex flex-col"
        style={{
          backgroundColor: effectiveBackgroundColor.color, // Panel background
          opacity: effectiveBackgroundColor.opacity,
          border: `${effectiveBorderWidth}px solid ${effectiveAccentColor}`, // Panel border with accent color
          borderRadius: effectiveCornerStyle === 'rounded' ? '0.375rem' : '0', // Panel border radius
        }}
      >
        
        {/* Event Information Section */}
        <div
          className="p-3 min-[48rem]:p-4 flex-shrink-0"
          style={{
            borderBottom: `${effectiveBorderWidth}px solid ${effectiveAccentColor}`,
          }}
        >
          <div className="flex gap-2 min-[48rem]:gap-3">
            <DateContainer dateTime={paymentData.eventId.dateTime} />
            <div className="flex-1">
              <h1 className={`text-lg min-[48rem]:text-xl font-bold ${effectiveTheme === 'dark' ? 'text-white' : 'text-black'}`}>
                {paymentData.eventId.title}
              </h1>
              <p className={`text-xs min-[48rem]:text-sm ${effectiveTheme === 'dark' ? 'text-gray-300' : 'text-gray-800'}`}>
                {paymentData.eventId.location.venue}
              </p>
              <p className={`text-xs ${effectiveTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                {paymentData.eventId.location.city}
              </p>
            </div>
          </div>
        </div>

        {/* Main Content - Scrollable */}
        <div className="flex-1 min-h-0 overflow-y-auto">
          <div className="p-3 min-[48rem]:p-4 space-y-4">
            
            {/* Customer Information */}
            <div className="space-y-3">
              <h2 className={`text-sm min-[48rem]:text-base font-semibold ${effectiveTheme === 'dark' ? 'text-white' : 'text-black'}`}>
                Customer Information
              </h2>
              
              {/* First Name and Last Name on same row */}
              <div className="grid grid-cols-2 gap-2">
                <Input
                  label="First Name"
                  value={customerInfo.firstName}
                  onChange={(e) => handleCustomerInfoChange('firstName', e.target.value)}
                  error={errors.firstName}
                  className="text-sm"
                />
                <Input
                  label="Last Name"
                  value={customerInfo.lastName}
                  onChange={(e) => handleCustomerInfoChange('lastName', e.target.value)}
                  error={errors.lastName}
                  className="text-sm"
                />
              </div>
              
              <Input
                label="Email"
                type="email"
                value={customerInfo.email}
                onChange={(e) => handleCustomerInfoChange('email', e.target.value)}
                error={errors.email}
                className="text-sm"
              />
              
              <Input
                label="Phone Number"
                type="tel"
                value={customerInfo.phone}
                onChange={(e) => handleCustomerInfoChange('phone', e.target.value)}
                helperText="Optional"
                className="text-sm"
              />
            </div>

            {/* Payment Method Selection */}
            <PaymentMethodSelector
              selectedMethod={selectedPaymentMethod}
              onMethodChange={setSelectedPaymentMethod}
              cardDetails={cardDetails}
              onCardDetailsChange={setCardDetails}
            />
            {errors.paymentMethod && (
              <p className="text-xs text-red-600">{errors.paymentMethod}</p>
            )}

            {/* Order Summary */}
            <div className="space-y-3">
              <h3 className={`text-sm font-semibold ${effectiveTheme === 'dark' ? 'text-white' : 'text-black'}`}>
                Order Summary
              </h3>
              <div
                className="p-3 space-y-2"
                style={{
                  border: `${effectiveBorderWidth}px solid ${effectiveAccentColor}`,
                  borderRadius: effectiveCornerStyle === 'rounded' ? '0.375rem' : '0',
                }}
              >
                {ticketSummary.map((item, index) => (
                  <div key={index} className="flex justify-between items-center text-sm">
                    <span className={effectiveTheme === 'dark' ? 'text-white' : 'text-black'}>
                      {item.quantity}x {item.name}
                    </span>
                    <span className={`font-medium ${effectiveTheme === 'dark' ? 'text-white' : 'text-black'}`}>
                      ${item.total.toFixed(2)}
                    </span>
                  </div>
                ))}
                <div
                  className="pt-2 mt-2"
                  style={{
                    borderTop: `${effectiveBorderWidth}px solid ${effectiveAccentColor}`,
                  }}
                >
                  <div className="flex justify-between items-center">
                    <span className={`font-semibold ${effectiveTheme === 'dark' ? 'text-white' : 'text-black'}`}>
                      Total
                    </span>
                    <span className={`text-lg font-bold ${effectiveTheme === 'dark' ? 'text-white' : 'text-black'}`}>
                      ${paymentData.totalAmount.toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Place Order Button */}
        <div
          className="p-3 min-[48rem]:p-4 flex-shrink-0"
          style={{
            borderTop: `${effectiveBorderWidth}px solid ${effectiveAccentColor}`,
          }}
        >
          <Button
            onClick={handlePlaceOrder}
            loading={isProcessing}
            className="w-full h-10 transition-all duration-300 text-white disabled:opacity-50"
            style={{
              backgroundColor: effectiveAccentColor,
              border: `${effectiveBorderWidth}px solid ${effectiveAccentColor}`,
              borderRadius: effectiveCornerStyle === 'rounded' ? '0.375rem' : '0',
            }}
            onMouseEnter={(e) => {
              if (!isProcessing) {
                e.currentTarget.style.opacity = '0.8';
                e.currentTarget.style.boxShadow = `0 0 12px ${effectiveAccentColor}`;
              }
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.opacity = '1';
              e.currentTarget.style.boxShadow = 'none';
            }}
            size="md"
          >
            {isProcessing ? 'Processing...' : 'Place Order & Pay'}
          </Button>
        </div>

        {/* Footer Section */}
        <div
          className="p-2 min-[48rem]:p-3 flex justify-between items-center flex-shrink-0"
          style={{
            backgroundColor: effectiveTheme === 'dark' ? 'rgba(0,0,0,0.2)' : 'rgba(255,255,255,0.2)',
            borderTop: `${effectiveBorderWidth}px solid ${effectiveAccentColor}`,
          }}
        >
          <span className={`text-xs ${effectiveTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
            Powered by EventDiscover
          </span>
          <LanguageSelector />
        </div>
      </div>
    </div>
  );
}
