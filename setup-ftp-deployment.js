#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up FTP deployment for your project...\n');

// 1. Copy the universal deployer
const deployerSource = `const ftp = require('basic-ftp');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
require('dotenv').config({ path: '.env.ftp' });

class UniversalFTPDeployer {
    constructor() {
        this.client = new ftp.Client();
        this.client.ftp.verbose = false;
        
        this.config = {
            host: process.env.FTP_HOST,
            user: process.env.FTP_USERNAME,
            password: process.env.FTP_PASSWORD,
            remoteDir: process.env.FTP_REMOTE_DIR || '/public_html'
        };
        
        this.uploadedFiles = 0;
        this.failedFiles = 0;
        this.projectConfig = this.detectProjectType();
    }

    detectProjectType() {
        const packageJson = this.readPackageJson();
        
        if (packageJson && packageJson.dependencies && packageJson.dependencies.next) {
            return this.getNextJSConfig();
        } else if (packageJson && packageJson.dependencies && packageJson.dependencies.react) {
            return this.getReactConfig();
        } else if (packageJson && packageJson.dependencies && packageJson.dependencies.vue) {
            return this.getVueConfig();
        } else if (fs.existsSync('index.html')) {
            return this.getStaticConfig();
        } else {
            return this.getNodeJSConfig();
        }
    }

    readPackageJson() {
        try {
            return JSON.parse(fs.readFileSync('package.json', 'utf8'));
        } catch {
            return null;
        }
    }

    getNextJSConfig() {
        console.log('🔍 Detected: Next.js project');
        return {
            type: 'nextjs',
            buildCommand: 'npm run build',
            essentialFiles: ['package.json', 'package-lock.json', 'server.js', '.npmrc', 'next.config.js'],
            buildDirs: ['.next'],
            sourceDirs: ['src', 'pages', 'components', 'styles', 'lib', 'utils', 'public'],
            excludePatterns: ['node_modules', '.git', '.env.*', 'deploy-ftp*.js', 'README.md']
        };
    }

    getReactConfig() {
        console.log('🔍 Detected: React project');
        return {
            type: 'react',
            buildCommand: 'npm run build',
            essentialFiles: ['package.json', 'package-lock.json', '.npmrc'],
            buildDirs: ['build', 'dist'],
            sourceDirs: ['src', 'public'],
            excludePatterns: ['node_modules', '.git', '.env.*', 'deploy-ftp*.js', 'README.md']
        };
    }

    getVueConfig() {
        console.log('🔍 Detected: Vue.js project');
        return {
            type: 'vue',
            buildCommand: 'npm run build',
            essentialFiles: ['package.json', 'package-lock.json', '.npmrc'],
            buildDirs: ['dist'],
            sourceDirs: ['src', 'public'],
            excludePatterns: ['node_modules', '.git', '.env.*', 'deploy-ftp*.js', 'README.md']
        };
    }

    getNodeJSConfig() {
        console.log('🔍 Detected: Node.js project');
        return {
            type: 'nodejs',
            buildCommand: null,
            essentialFiles: ['package.json', 'package-lock.json', '.npmrc', 'server.js', 'index.js', 'app.js'],
            buildDirs: [],
            sourceDirs: ['src', 'lib', 'routes', 'controllers', 'models', 'public'],
            excludePatterns: ['node_modules', '.git', '.env.*', 'deploy-ftp*.js', 'README.md']
        };
    }

    getStaticConfig() {
        console.log('🔍 Detected: Static HTML project');
        return {
            type: 'static',
            buildCommand: null,
            essentialFiles: ['index.html'],
            buildDirs: [],
            sourceDirs: ['css', 'js', 'images', 'assets'],
            excludePatterns: ['.git', 'deploy-ftp*.js', 'README.md', '*.md']
        };
    }

    async connect() {
        console.log('🔌 Connecting to FTP server...');
        try {
            await this.client.access(this.config);
            console.log('✅ Connected to FTP server successfully');
        } catch (error) {
            console.error('❌ Failed to connect to FTP server:', error.message);
            throw error;
        }
    }

    async buildProject() {
        if (this.projectConfig.buildCommand) {
            console.log(\`🏗️  Building \${this.projectConfig.type} project...\`);
            try {
                execSync(this.projectConfig.buildCommand, { stdio: 'inherit' });
                console.log('✅ Build completed successfully');
            } catch (error) {
                console.error('❌ Build failed:', error.message);
                throw error;
            }
        } else {
            console.log('⏭️  No build step required for this project type');
        }
    }

    shouldExclude(filePath) {
        return this.projectConfig.excludePatterns.some(pattern => 
            filePath.includes(pattern) || 
            filePath.endsWith(pattern) ||
            filePath.match(pattern.replace('*', '.*'))
        );
    }

    async createRemoteDir(remotePath) {
        try {
            const parts = remotePath.split('/').filter(p => p);
            let currentPath = '';
            
            for (const part of parts) {
                currentPath += '/' + part;
                try {
                    await this.client.cd(currentPath);
                } catch {
                    try {
                        await this.client.send('MKD ' + currentPath);
                    } catch (mkdError) {
                        // Directory might already exist
                    }
                }
            }
        } catch (error) {
            console.log(\`⚠️  Could not create directory \${remotePath}\`);
        }
    }

    async uploadSingleFile(localPath, remotePath) {
        try {
            const parentDir = path.dirname(remotePath);
            if (parentDir !== '.' && parentDir !== '/') {
                await this.createRemoteDir(parentDir);
            }
            
            await this.client.uploadFrom(localPath, remotePath);
            this.uploadedFiles++;
            console.log(\`✅ \${this.uploadedFiles}: \${remotePath}\`);
            return true;
        } catch (error) {
            this.failedFiles++;
            console.log(\`❌ Failed: \${localPath} -> \${error.message}\`);
            return false;
        }
    }

    async uploadDirectoryContents(localDir, remoteDir, prefix = '') {
        if (!fs.existsSync(localDir)) return;
        
        const items = fs.readdirSync(localDir);
        
        for (const item of items) {
            const localPath = path.join(localDir, item);
            const remotePath = \`\${remoteDir}/\${item}\`.replace(/\\\\/g, '/').replace(/\\/+/g, '/');
            
            if (this.shouldExclude(localPath)) {
                continue;
            }
            
            const stat = fs.statSync(localPath);
            
            if (stat.isDirectory()) {
                console.log(\`📁 Processing directory: \${prefix}\${item}/\`);
                await this.uploadDirectoryContents(localPath, remotePath, prefix + '  ');
            } else {
                await this.uploadSingleFile(localPath, remotePath);
            }
        }
    }

    async deploy() {
        try {
            console.log('🚀 Starting Universal FTP deployment...');
            console.log(\`📍 Target: \${this.config.host}\${this.config.remoteDir}\`);
            console.log(\`🎯 Project Type: \${this.projectConfig.type.toUpperCase()}\`);
            
            await this.buildProject();
            await this.connect();
            await this.createRemoteDir(this.config.remoteDir);
            
            console.log('📦 Uploading essential files...');
            for (const file of this.projectConfig.essentialFiles) {
                if (fs.existsSync(file)) {
                    await this.uploadSingleFile(file, \`\${this.config.remoteDir}/\${file}\`);
                }
            }
            
            for (const dir of this.projectConfig.buildDirs) {
                if (fs.existsSync(dir)) {
                    console.log(\`🏗️  Uploading \${dir} directory...\`);
                    await this.uploadDirectoryContents(dir, \`\${this.config.remoteDir}/\${dir}\`);
                }
            }
            
            for (const dir of this.projectConfig.sourceDirs) {
                if (fs.existsSync(dir)) {
                    console.log(\`📂 Uploading \${dir} directory...\`);
                    await this.uploadDirectoryContents(dir, \`\${this.config.remoteDir}/\${dir}\`);
                }
            }
            
            console.log(\`\\n🎉 Deployment Summary:\`);
            console.log(\`✅ Successfully uploaded: \${this.uploadedFiles} files\`);
            console.log(\`❌ Failed uploads: \${this.failedFiles} files\`);
            
            this.printPostDeploymentInstructions();
            
        } catch (error) {
            console.error('💥 Deployment failed:', error.message);
            process.exit(1);
        } finally {
            this.client.close();
        }
    }

    printPostDeploymentInstructions() {
        console.log('\\n📝 Next steps on server:');
        console.log('   1. SSH into your server');
        console.log(\`   2. cd \${this.config.remoteDir}\`);
        
        if (this.projectConfig.type === 'nextjs' || this.projectConfig.type === 'nodejs') {
            console.log('   3. npm install');
            console.log('   4. npm start');
        } else if (this.projectConfig.type === 'react' || this.projectConfig.type === 'vue') {
            console.log('   3. Serve the build/dist folder with a web server');
        } else if (this.projectConfig.type === 'static') {
            console.log('   3. Your static files are ready to serve!');
        }
    }
}

const deployer = new UniversalFTPDeployer();
deployer.deploy();`;

// 2. Create .env.ftp template
const envTemplate = `# FTP Deployment Configuration
FTP_HOST=your-ftp-host.com
FTP_USERNAME=your-ftp-username
FTP_PASSWORD=your-ftp-password
FTP_REMOTE_DIR=/public_html/your-app-folder`;

// 3. Create .npmrc for production installs
const npmrcContent = `production=true`;

// Write files
try {
    fs.writeFileSync('deploy-ftp.js', deployerSource);
    console.log('✅ Created deploy-ftp.js');
    
    if (!fs.existsSync('.env.ftp')) {
        fs.writeFileSync('.env.ftp', envTemplate);
        console.log('✅ Created .env.ftp template');
    } else {
        console.log('⏭️  .env.ftp already exists, skipping');
    }
    
    if (!fs.existsSync('.npmrc')) {
        fs.writeFileSync('.npmrc', npmrcContent);
        console.log('✅ Created .npmrc');
    } else {
        console.log('⏭️  .npmrc already exists, skipping');
    }
    
    // Update package.json if it exists
    if (fs.existsSync('package.json')) {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        
        if (!packageJson.scripts) {
            packageJson.scripts = {};
        }
        
        packageJson.scripts['deploy:ftp'] = 'node deploy-ftp.js';
        packageJson.scripts['deploy:ftp:full'] = 'npm run build && npm run deploy:ftp';
        
        if (!packageJson.dependencies || !packageJson.dependencies['basic-ftp']) {
            if (!packageJson.dependencies) packageJson.dependencies = {};
            packageJson.dependencies['basic-ftp'] = '^5.0.5';
        }
        
        if (!packageJson.dependencies['dotenv']) {
            packageJson.dependencies['dotenv'] = '^16.0.0';
        }
        
        fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
        console.log('✅ Updated package.json with deployment scripts');
    }
    
    console.log('\\n🎉 FTP deployment setup complete!');
    console.log('\\n📝 Next steps:');
    console.log('   1. Edit .env.ftp with your FTP credentials');
    console.log('   2. Run: npm install (to get basic-ftp and dotenv)');
    console.log('   3. Deploy with: npm run deploy:ftp:full');
    
} catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
}`;

fs.writeFileSync('setup-ftp-deployment.js', setupScript);
console.log('✅ Created setup-ftp-deployment.js');
