import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactNode } from 'react';
import { useEvents, useEvent } from '../useEvents';
import * as eventService from '@/services/eventService';

// Mock the event service
jest.mock('@/services/eventService');
const mockedEventService = eventService as jest.Mocked<typeof eventService>;

// Create a wrapper for React Query
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useEvents Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('fetches events successfully', async () => {
    const mockData = {
      success: true,
      data: {
        data: [
          {
            _id: '1',
            title: 'Test Event',
            dateTime: '2024-12-25T19:00:00Z',
          },
        ],
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalItems: 1,
        },
      },
    };

    mockedEventService.getPublicEvents.mockResolvedValue(mockData);

    const { result } = renderHook(() => useEvents({ page: 1, limit: 10 }), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(mockData);
    expect(mockedEventService.getPublicEvents).toHaveBeenCalledWith({
      page: 1,
      limit: 10,
    });
  });

  it('handles errors correctly', async () => {
    const error = new Error('Failed to fetch events');
    mockedEventService.getPublicEvents.mockRejectedValue(error);

    const { result } = renderHook(() => useEvents(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(result.current.error).toEqual(error);
  });
});

describe('useEvent Hook', () => {
  it('fetches a single event successfully', async () => {
    const mockData = {
      success: true,
      data: {
        _id: '1',
        title: 'Test Event',
        dateTime: '2024-12-25T19:00:00Z',
      },
    };

    mockedEventService.getPublicEvent.mockResolvedValue(mockData);

    const { result } = renderHook(() => useEvent('1'), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(mockData);
    expect(mockedEventService.getPublicEvent).toHaveBeenCalledWith('1');
  });


});
