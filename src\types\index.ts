export interface Event {
  _id: string;
  title: string;
  description: string;
  dateTime: string;
  location: {
    venue: string;
    address: string;
    city: string;
    coordinates: [number, number];
  };
  image?: {
    url: string;
    alt: string;
  };
  promoterId: {
    _id: string;
    name: string;
    socials?: {
      website?: string;
      facebook?: string;
      instagram?: string;
      twitter?: string;
    };
  };
  priceCategories: PriceCategory[];
  category: string;
  tags: string[];
  capacity?: number;
  ageRestriction?: number;
  salesActive: boolean;
  availableTickets: number;
}

export interface PriceCategory {
  _id: string;
  name: string;
  price: number;
  totalTickets: number;
  soldTickets: number;
  availableTickets: number;
  description?: string;
}

export interface Promoter {
  _id: string;
  name: string;
  description?: string;
  profileImage?: {
    url: string;
    alt: string;
  };
  socials: {
    website?: string;
    facebook?: string;
    instagram?: string;
    twitter?: string;
  };
  createdAt: string;
  stats?: {
    totalEvents: number;
    upcomingEvents: number;
    pastEvents?: number;
  };
  recentEvents?: Array<{
    _id: string;
    title: string;
    description?: string;
    dateTime: string;
    location: {
      city: string;
      venue?: string;
      address?: string;
      coordinates?: [number, number];
    };
    category: string;
    availableTickets: number | null;
    salesActive: boolean;
    eventStatus: string;
    daysUntilEvent: number;
    id: string;
  }>;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
  };
}

export interface PromotersPaginatedResponse {
  promoters: Promoter[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalPromoters: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
    limit: number;
  };
}

export interface EventFilters {
  page?: number;
  limit?: number;
  category?: string;
  city?: string;
  dateFrom?: string;
  dateTo?: string;
  promoterId?: string;
}

export interface PromoterFilters {
  page?: number;
  limit?: number;
}

// Checkout and Payment Types
export interface CategorySelection {
  categoryId: string;
  quantity: number;
}

export interface CustomerInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
}

export interface CardDetails {
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  cardholderName: string;
}

export interface PaymentData {
  eventId: string;
  selectedCategories: CategorySelection[];
  customerInfo: CustomerInfo;
  paymentMethod: 'stripe' | 'google-pay' | 'apple-pay';
  cardDetails?: CardDetails;
  promoCode?: string;
  totalAmount: number;
}

// Portal Settings Types
export interface ColorWithOpacity {
  color: string;
  opacity: number;
}

export interface Typography {
  fontFamily: string;
}

export interface PanelSettings {
  cornerStyle: 'rounded' | 'square';
}

export interface GlobalSettings {
  theme: 'light' | 'dark';
  accentColor: string;
  borderWidth: number;
  typography: Typography;
  backgroundColor: ColorWithOpacity;
  panelSettings: PanelSettings;
}

export interface BackgroundOptions {
  type: 'color' | 'gradient' | 'image' | 'video';
  value: string;
  makeDefault: boolean;
}

export interface LinkStyling {
  backgroundColor: ColorWithOpacity | null;
  cornerStyle: 'rounded' | 'square' | null;
}

export interface LinkPageSettings {
  backgroundOptions: BackgroundOptions;
  linkStyling: LinkStyling;
}

export interface EventPageSettings {
  backgroundOptions: BackgroundOptions;
}

export interface PortalSettings {
  _id: string;
  promoterId: string;
  globalSettings: GlobalSettings;
  linkPageSettings: LinkPageSettings;
  eventPageSettings: EventPageSettings;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface ResolvedLinkPageSettings {
  backgroundOptions: BackgroundOptions;
  linkStyling: {
    backgroundColor: ColorWithOpacity;
    cornerStyle: 'rounded' | 'square';
    borderWidth: number;
    typography: Typography;
  };
}

export interface ResolvedEventPageSettings {
  backgroundOptions: BackgroundOptions;
  panelSettings: {
    backgroundColor: ColorWithOpacity;
    cornerStyle: 'rounded' | 'square';
    borderWidth: number;
    typography: Typography;
  };
}

export interface ResolvedSettings {
  global: GlobalSettings;
  linkPage: ResolvedLinkPageSettings;
  eventPage: ResolvedEventPageSettings;
}

export interface PortalSettingsResponse {
  userId: string;
  settings: PortalSettings & {
    resolvedSettings: ResolvedSettings;
    id: string;
  };
  resolvedSettings: ResolvedSettings;
}
