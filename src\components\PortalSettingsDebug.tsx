'use client';

import { useEffect, useState } from 'react';
import { usePortalTheme } from '@/hooks/usePortalSettings';

interface PortalSettingsDebugProps {
  promoterId: string;
}

export function PortalSettingsDebug({ promoterId }: PortalSettingsDebugProps) {
  const [directApiTest, setDirectApiTest] = useState<any>(null);
  const [directApiError, setDirectApiError] = useState<string | null>(null);

  const {
    resolvedSettings,
    isLoading,
    error,
    theme,
    accentColor,
    borderWidth,
    cornerStyle,
    fontFamily,
    backgroundColor,
    linkPageSettings,
  } = usePortalTheme(promoterId);

  // Test direct API call
  useEffect(() => {
    const testDirectApi = async () => {
      try {
        const response = await fetch(`http://localhost:3000/api/portal-settings/view/${promoterId}`);
        const data = await response.json();
        setDirectApiTest(data);
        console.log('Direct API test successful:', data);
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : 'Unknown error';
        setDirectApiError(errorMsg);
        console.error('Direct API test failed:', err);
      }
    };

    if (promoterId) {
      testDirectApi();
    }
  }, [promoterId]);

  console.log('Portal Settings Debug:', {
    promoterId,
    isLoading,
    error: error?.message,
    resolvedSettings,
    theme,
    accentColor,
    backgroundColor,
    linkPageSettings,
    directApiTest,
    directApiError
  });

  if (isLoading) {
    return (
      <div className="fixed top-4 right-4 p-4 bg-yellow-100 border border-yellow-400 rounded shadow-lg max-w-sm text-xs z-50">
        Loading portal settings...
      </div>
    );
  }

  if (error) {
    return (
      <div className="fixed top-4 right-4 p-4 bg-red-100 border border-red-400 rounded shadow-lg max-w-sm text-xs z-50">
        <h3 className="font-bold mb-2">Portal Settings Error</h3>
        <div>Error: {error.message}</div>
      </div>
    );
  }

  if (!resolvedSettings) {
    return (
      <div className="fixed top-4 right-4 p-4 bg-gray-100 border border-gray-400 rounded shadow-lg max-w-sm text-xs z-50">
        No portal settings found
      </div>
    );
  }

  return (
    <div className="fixed top-4 right-4 p-4 bg-white border border-gray-300 rounded shadow-lg max-w-sm text-xs z-50 max-h-96 overflow-y-auto">
      <h3 className="font-bold mb-2">Portal Settings Debug</h3>
      <div className="space-y-1">
        <div><strong>Promoter ID:</strong> {promoterId}</div>
        <div><strong>Hook Loading:</strong> {isLoading ? 'Yes' : 'No'}</div>
        <div><strong>Hook Error:</strong> {error ? String(error) : 'None'}</div>

        <div className="mt-2 pt-2 border-t">
          <strong>Direct API Test:</strong>
          {directApiError ? (
            <div className="text-red-600">Error: {directApiError}</div>
          ) : directApiTest ? (
            <div className="text-green-600">Success ✓</div>
          ) : (
            <div>Testing...</div>
          )}
        </div>

        {resolvedSettings && (
          <>
            <div className="mt-2 pt-2 border-t">
              <div><strong>Theme:</strong> {theme}</div>
              <div><strong>Accent Color:</strong> <span style={{ color: accentColor }}>{accentColor}</span></div>
              <div><strong>Border Width:</strong> {borderWidth}px</div>
              <div><strong>Corner Style:</strong> {cornerStyle}</div>
              <div><strong>Font Family:</strong> {fontFamily}</div>
              <div><strong>Background:</strong> <span style={{ color: backgroundColor.color }}>{backgroundColor.color}</span> (opacity: {backgroundColor.opacity})</div>
              {linkPageSettings && (
                <>
                  <div><strong>Page BG Type:</strong> {linkPageSettings.backgroundOptions.type}</div>
                  <div><strong>Page BG Value:</strong> <span style={{ color: linkPageSettings.backgroundOptions.value }}>{linkPageSettings.backgroundOptions.value}</span></div>
                  <div><strong>Link BG:</strong> <span style={{ color: linkPageSettings.linkStyling.backgroundColor.color }}>{linkPageSettings.linkStyling.backgroundColor.color}</span></div>
                </>
              )}
            </div>

            <div className="mt-2 pt-2 border-t">
              <strong>CSS Variables:</strong>
              <div>--portal-accent-color: {typeof document !== 'undefined' ? getComputedStyle(document.documentElement).getPropertyValue('--portal-accent-color') : 'N/A'}</div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
