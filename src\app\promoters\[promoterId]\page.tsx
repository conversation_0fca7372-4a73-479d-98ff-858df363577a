'use client';

import { useParams } from 'next/navigation';
import Link from 'next/link';
import { useEffect } from 'react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { usePromoter } from '@/hooks/usePromoters';
import { usePortalTheme } from '@/hooks/usePortalSettings';
import { LanguageSelector } from '@/components/LanguageSelector';
import { PortalSettingsDebug } from '@/components/PortalSettingsDebug';
import { applyCSSVariables, getBackgroundStyle, getThemeClasses } from '@/lib/portalSettings';




export default function PromoterPage() {
  const params = useParams();
  const promoterId = params.promoterId as string;

  // Use React Query to fetch real data from API
  const { data: promoterData, isLoading: promoterLoading, error: promoterError } = usePromoter(promoterId);

  // Fetch portal settings for this promoter
  const {
    resolvedSettings,
    isLoading: settingsLoading,
    theme,
    accentColor,
    borderWidth,
    cornerStyle,
    fontFamily,
    backgroundColor,
    linkPageSettings,
  } = usePortalTheme(promoterId);

  // Get promoter data from API
  const promoter = promoterData?.data;
  // Get events from the promoter API response
  const events = promoter?.recentEvents || [];

  // Apply CSS variables when settings are loaded
  useEffect(() => {
    if (resolvedSettings) {
      applyCSSVariables(resolvedSettings);
    }
  }, [resolvedSettings]);

  // Show loading state while fetching promoter or settings
  if (promoterLoading || settingsLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <LoadingSpinner size="lg" className="text-white mb-4" />
          <p>Loading promoter details...</p>
        </div>
      </div>
    );
  }

  if (promoterError || !promoter) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <h1 className="text-2xl font-bold mb-4">Promoter Not Found</h1>
          <p>The promoter you're looking for doesn't exist or has been removed.</p>
        </div>
      </div>
    );
  }

  // Fallback portal settings for demonstration if API fails
  const fallbackSettings = {
    theme: 'light',
    accentColor: '#f43f5e', // Rose
    borderWidth: 1,
    cornerStyle: 'square',
    fontFamily: 'Arial, sans-serif',
    backgroundColor: { color: '#06b6d4', opacity: 1 }, // Cyan
    linkPageSettings: {
      backgroundOptions: { type: 'color', value: '#f97316' }, // Orange
      linkStyling: {
        backgroundColor: { color: '#84cc16', opacity: 1 }, // Lime green
        cornerStyle: 'square'
      }
    }
  };

  // Use fallback if portal settings are not loaded
  const effectiveTheme = theme || fallbackSettings.theme;
  const effectiveAccentColor = accentColor || fallbackSettings.accentColor;
  const effectiveBorderWidth = borderWidth || fallbackSettings.borderWidth;
  const effectiveCornerStyle = cornerStyle || fallbackSettings.cornerStyle;
  const effectiveFontFamily = fontFamily || fallbackSettings.fontFamily;
  const effectiveBackgroundColor = backgroundColor || fallbackSettings.backgroundColor;
  const effectiveLinkPageSettings = linkPageSettings || fallbackSettings.linkPageSettings;

  // Get background style from link page settings
  const pageBackgroundStyle = effectiveLinkPageSettings?.backgroundOptions
    ? getBackgroundStyle(effectiveLinkPageSettings.backgroundOptions)
    : {};

  // Get link styling from portal settings
  const linkStyling = effectiveLinkPageSettings?.linkStyling || {};
  const linkBackgroundColor = linkStyling.backgroundColor?.color || effectiveBackgroundColor.color;
  const linkOpacity = linkStyling.backgroundColor?.opacity || effectiveBackgroundColor.opacity;
  const linkCornerStyle = linkStyling.cornerStyle || effectiveCornerStyle;

  return (
    <>
      <PortalSettingsDebug promoterId={promoterId} />
      <div
        className={`min-h-screen flex items-center justify-center p-2 ${getThemeClasses(effectiveTheme, effectiveCornerStyle)}`}
        style={{
          ...pageBackgroundStyle,
          fontFamily: effectiveFontFamily, // Typography applies to whole page
        }}
      >
      <div
        className="w-full md:w-2/5 min-[48rem]:w-[28%] max-w-[800px] max-h-[95vh] mx-auto shadow-2xl overflow-hidden flex flex-col"
        style={{
          backgroundColor: effectiveBackgroundColor.color, // Panel background
          opacity: effectiveBackgroundColor.opacity,
          border: `${effectiveBorderWidth}px solid ${effectiveAccentColor}`, // Panel border with accent color
          borderRadius: effectiveCornerStyle === 'rounded' ? '0.375rem' : '0', // Panel border radius
        }}
      >
        {/* Promoter Information Section */}
        <div
          className="p-3 min-[48rem]:p-4 flex-shrink-0"
          style={{
            borderBottom: `${effectiveBorderWidth}px solid ${effectiveAccentColor}`,
          }}
        >
          <div className="flex gap-2 min-[48rem]:gap-3">
            {/* Profile Image */}
            <div
              className="flex-shrink-0 w-10 h-10 min-[48rem]:w-12 min-[48rem]:h-12 flex items-center justify-center text-white"
              style={{
                backgroundColor: effectiveAccentColor,
                border: `${effectiveBorderWidth}px solid ${effectiveAccentColor}`,
                borderRadius: effectiveCornerStyle === 'rounded' ? '0.375rem' : '0',
              }}
            >
              {promoter.profileImage ? (
                <img
                  src={promoter.profileImage.url}
                  alt={promoter.profileImage.alt}
                  className="w-full h-full object-cover"
                  style={{
                    borderRadius: effectiveCornerStyle === 'rounded' ? '0.375rem' : '0',
                  }}
                />
              ) : (
                <span className="text-sm min-[48rem]:text-base font-bold">
                  {promoter.name.charAt(0).toUpperCase()}
                </span>
              )}
            </div>
            <div className="flex-1">
              <h1 className={`text-lg min-[48rem]:text-xl font-bold ${effectiveTheme === 'dark' ? 'text-white' : 'text-black'}`}>
                {promoter.name}
              </h1>
              {promoter.stats && (
                <p className={`text-xs min-[48rem]:text-sm ${effectiveTheme === 'dark' ? 'text-gray-300' : 'text-gray-800'}`}>
                  {promoter.stats.totalEvents} Total Events
                </p>
              )}
              {promoter.description && (
                <p className={`text-xs line-clamp-2 ${effectiveTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                  {promoter.description}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Events Section */}
        <div className="flex-1 min-h-0 flex flex-col">
          <div className="p-3 min-[48rem]:p-4 flex-1 overflow-y-auto">
            <h2 className={`text-sm min-[48rem]:text-base font-semibold mb-2 ${effectiveTheme === 'dark' ? 'text-white' : 'text-black'}`}>
              Events
            </h2>
            {promoterLoading ? (
              <div className="text-center py-8">
                <LoadingSpinner size="md" className="text-blue-500" />
                <p className={`mt-2 ${effectiveTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                  Loading events...
                </p>
              </div>
            ) : events.length > 0 ? (
              <div className="space-y-1">
                {events.map((event) => (
                  <Link
                    key={event._id || event.id}
                    href={`/events/${event._id || event.id}/payment`}
                    className="block p-2 transition-all duration-300 hover:opacity-80"
                    style={{
                      backgroundColor: linkBackgroundColor,
                      opacity: linkOpacity,
                      border: `${borderWidth}px solid ${accentColor}`,
                      borderRadius: linkCornerStyle === 'rounded' ? '0.375rem' : '0',
                      boxShadow: `0 0 0 0 ${accentColor}`,
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.boxShadow = `0 0 10px ${accentColor}`;
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.boxShadow = `0 0 0 0 ${accentColor}`;
                    }}
                  >
                    <h3 className={`text-sm min-[48rem]:text-base font-semibold mb-1 ${theme === 'dark' ? 'text-white' : 'text-black'}`}>
                      {event.title}
                    </h3>
                    <div className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                      <span>{new Date(event.dateTime).toLocaleDateString()}</span>
                      {event.location && (
                        <span className="ml-2">• {event.location.venue}</span>
                      )}
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                  No events available
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Footer Section */}
        <div
          className="p-2 min-[48rem]:p-3 flex justify-between items-center flex-shrink-0"
          style={{
            backgroundColor: theme === 'dark' ? 'rgba(0,0,0,0.2)' : 'rgba(255,255,255,0.2)',
            borderTop: `${borderWidth}px solid ${accentColor}`,
          }}
        >
          <span className={`text-xs ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
            Powered by EventDiscover
          </span>
          <LanguageSelector />
        </div>
      </div>
    </div>
    </>
  );
}
