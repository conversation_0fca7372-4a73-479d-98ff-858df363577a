'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Users } from 'lucide-react';
import { Layout } from '@/components/Layout';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Button } from '@/components/ui/Button';
import { usePromoters } from '@/hooks/usePromoters';



export default function PromotersPage() {
  const [page, setPage] = useState(1);

  // Use React Query to fetch real data from API
  const { data, isLoading, error } = usePromoters({
    page,
    limit: 12
  });

  // Only use real data from API
  const promoters = data?.data?.promoters || [];
  const pagination = data?.data?.pagination || {
    currentPage: 1,
    totalPages: 1,
    totalPromoters: 0,
    hasNextPage: false,
    hasPrevPage: false,
    limit: 12
  };

  return (
    <Layout>
      {/* Header */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            <h1 className="font-display text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Event Promoters
            </h1>
            <p className="text-xl text-gray-600">
              Discover amazing events from trusted promoters around the world
            </p>
          </div>
        </div>
      </section>

      {/* Promoters Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {isLoading ? (
            <div className="text-center">
              <LoadingSpinner size="lg" className="text-primary-500" />
              <p className="mt-4 text-gray-600">Loading promoters...</p>
            </div>
          ) : error ? (
            <div className="text-center">
              <p className="text-red-600">Failed to load promoters. Please try again later.</p>
            </div>
          ) : promoters.length === 0 ? (
            <div className="text-center">
              <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-600 text-lg">
                No promoters found.
              </p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {promoters.map((promoter) => (
                  <Link
                    key={promoter._id}
                    href={`/promoters/${promoter._id}`}
                    className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group"
                  >
                    <div className="text-center">
                      <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
                        <span className="text-white font-bold text-2xl">
                          {promoter.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      
                      <h3 className="font-semibold text-lg text-gray-900 group-hover:text-primary-600 transition-colors mb-2">
                        {promoter.name}
                      </h3>

                      {promoter.description && (
                        <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                          {promoter.description}
                        </p>
                      )}

                      {promoter.stats && (
                        <div className="text-sm text-gray-600 mb-4">
                          <p>{promoter.stats.totalEvents} total events</p>
                          <p>{promoter.stats.upcomingEvents} upcoming</p>
                        </div>
                      )}

                      {/* Social Links Count */}
                      {promoter.socials && (
                        <div className="flex justify-center space-x-1 text-gray-400 text-xs">
                          {promoter.socials.website && <span>Website</span>}
                          {promoter.socials.facebook && <span>Facebook</span>}
                          {promoter.socials.instagram && <span>Instagram</span>}
                          {promoter.socials.twitter && <span>Twitter</span>}
                        </div>
                      )}

                      <div className="mt-4 text-primary-600 group-hover:text-primary-700 font-medium text-sm">
                        View Events →
                      </div>
                    </div>
                  </Link>
                ))}
              </div>

              {/* Pagination */}
              {pagination && pagination.totalPages > 1 && (
                <div className="mt-12 flex justify-center">
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => setPage(page - 1)}
                      disabled={page <= 1}
                    >
                      Previous
                    </Button>
                    
                    <span className="flex items-center px-4 py-2 text-gray-600">
                      Page {page} of {pagination.totalPages}
                    </span>
                    
                    <Button
                      variant="outline"
                      onClick={() => setPage(page + 1)}
                      disabled={page >= pagination.totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </section>
    </Layout>
  );
}
