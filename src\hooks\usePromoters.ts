import { useQuery } from '@tanstack/react-query';
import { getPublicPromoters, getPublicPromoter } from '@/services/eventService';
import { PromoterFilters } from '@/types';

export const usePromoters = (params: PromoterFilters = {}) => {
  return useQuery({
    queryKey: ['promoters', params],
    queryFn: () => getPublicPromoters(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
  });
};

export const usePromoter = (promoterId: string) => {
  return useQuery({
    queryKey: ['promoter', promoterId],
    queryFn: () => getPublicPromoter(promoterId),
    enabled: !!promoterId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};
