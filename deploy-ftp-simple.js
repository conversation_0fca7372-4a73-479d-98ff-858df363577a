const ftp = require('basic-ftp');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
require('dotenv').config({ path: '.env.ftp' });

class SimpleFTPDeployer {
    constructor() {
        this.client = new ftp.Client();
        this.client.ftp.verbose = false; // Reduce verbosity
        
        this.config = {
            host: process.env.FTP_HOST,
            user: process.env.FTP_USERNAME,
            password: process.env.FTP_PASSWORD,
            remoteDir: process.env.FTP_REMOTE_DIR || '/public_html'
        };
        
        this.uploadedFiles = 0;
        this.failedFiles = 0;
    }

    async connect() {
        console.log('🔌 Connecting to FTP server...');
        try {
            await this.client.access(this.config);
            console.log('✅ Connected to FTP server successfully');
        } catch (error) {
            console.error('❌ Failed to connect to FTP server:', error.message);
            throw error;
        }
    }

    async buildProject() {
        console.log('🏗️  Building Next.js project...');
        try {
            execSync('npm run build', { stdio: 'inherit' });
            console.log('✅ Build completed successfully');
        } catch (error) {
            console.error('❌ Build failed:', error.message);
            throw error;
        }
    }

    async createRemoteDir(remotePath) {
        try {
            const parts = remotePath.split('/').filter(p => p);
            let currentPath = '';
            
            for (const part of parts) {
                currentPath += '/' + part;
                try {
                    await this.client.cd(currentPath);
                } catch {
                    try {
                        await this.client.send('MKD ' + currentPath);
                    } catch (mkdError) {
                        // Directory might already exist
                    }
                }
            }
        } catch (error) {
            console.log(`⚠️  Could not create directory ${remotePath}`);
        }
    }

    async uploadSingleFile(localPath, remotePath) {
        try {
            // Ensure parent directory exists
            const parentDir = path.dirname(remotePath);
            if (parentDir !== '.' && parentDir !== '/') {
                await this.createRemoteDir(parentDir);
            }
            
            await this.client.uploadFrom(localPath, remotePath);
            this.uploadedFiles++;
            console.log(`✅ ${this.uploadedFiles}: ${remotePath}`);
            return true;
        } catch (error) {
            this.failedFiles++;
            console.log(`❌ Failed: ${localPath} -> ${error.message}`);
            return false;
        }
    }

    async uploadDirectoryContents(localDir, remoteDir, prefix = '') {
        const items = fs.readdirSync(localDir);
        
        for (const item of items) {
            const localPath = path.join(localDir, item);
            const remotePath = `${remoteDir}/${item}`.replace(/\\/g, '/').replace(/\/+/g, '/');
            
            // Skip certain files/folders
            if (item === 'node_modules' || item === '.git' || item.startsWith('.env')) {
                continue;
            }
            
            const stat = fs.statSync(localPath);
            
            if (stat.isDirectory()) {
                console.log(`📁 Processing directory: ${prefix}${item}/`);
                await this.uploadDirectoryContents(localPath, remotePath, prefix + '  ');
            } else {
                await this.uploadSingleFile(localPath, remotePath);
            }
        }
    }

    async deploy() {
        try {
            console.log('🚀 Starting simple FTP deployment...');
            console.log(`📍 Target: ${this.config.host}${this.config.remoteDir}`);
            
            // Build the project
            await this.buildProject();
            
            // Connect to FTP
            await this.connect();
            
            // Create base remote directory
            await this.createRemoteDir(this.config.remoteDir);
            
            // Upload essential files first
            console.log('📦 Uploading essential files...');
            const essentialFiles = ['package.json', 'package-lock.json', 'server.js', '.npmrc'];

            for (const file of essentialFiles) {
                if (fs.existsSync(file)) {
                    await this.uploadSingleFile(file, `${this.config.remoteDir}/${file}`);
                }
            }
            
            // Upload .next directory (most important)
            if (fs.existsSync('.next')) {
                console.log('🏗️  Uploading .next directory...');
                await this.uploadDirectoryContents('.next', `${this.config.remoteDir}/.next`);
            }
            
            // Upload public directory
            if (fs.existsSync('public')) {
                console.log('🌐 Uploading public directory...');
                await this.uploadDirectoryContents('public', `${this.config.remoteDir}/public`);
            }
            
            // Upload other important directories
            const otherDirs = ['pages', 'components', 'styles', 'lib', 'utils'];
            for (const dir of otherDirs) {
                if (fs.existsSync(dir)) {
                    console.log(`📂 Uploading ${dir} directory...`);
                    await this.uploadDirectoryContents(dir, `${this.config.remoteDir}/${dir}`);
                }
            }
            
            console.log('\n🎉 Deployment Summary:');
            console.log(`✅ Successfully uploaded: ${this.uploadedFiles} files`);
            console.log(`❌ Failed uploads: ${this.failedFiles} files`);
            
            if (this.failedFiles === 0) {
                console.log('\n🎊 Deployment completed successfully!');
            } else {
                console.log('\n⚠️  Deployment completed with some failures');
            }
            
            console.log('\n📝 Next steps on cPanel:');
            console.log('   1. SSH into your server');
            console.log(`   2. cd ${this.config.remoteDir}`);
            console.log('   3. npm install  # (production mode set by .npmrc)');
            console.log('   4. npm start');
            
        } catch (error) {
            console.error('💥 Deployment failed:', error.message);
            process.exit(1);
        } finally {
            this.client.close();
        }
    }
}

// Run deployment
const deployer = new SimpleFTPDeployer();
deployer.deploy();
