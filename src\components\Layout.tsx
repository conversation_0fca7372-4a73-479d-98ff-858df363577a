import { ReactNode } from 'react';
import { Header } from './Header';
import { Footer } from './Footer';
import { ApiStatus } from './ApiStatus';

interface LayoutProps {
  children: ReactNode;
  className?: string;
}

export function Layout({ children, className = '' }: LayoutProps) {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className={`flex-1 ${className}`}>
        {children}
      </main>
      <Footer />
      <ApiStatus />
    </div>
  );
}
