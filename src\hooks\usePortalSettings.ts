import { useQuery } from '@tanstack/react-query';
import { getPortalSettings } from '@/services/eventService';
import { PortalSettingsResponse, ResolvedSettings } from '@/types';

export const usePortalSettings = (promoterId: string) => {
  console.log('usePortalSettings called with promoterId:', promoterId);

  const result = useQuery({
    queryKey: ['portal-settings', promoterId],
    queryFn: () => {
      console.log('Fetching portal settings for promoterId:', promoterId);
      return getPortalSettings(promoterId);
    },
    enabled: !!promoterId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
  });

  console.log('Portal settings query result:', result);

  return result;
};

// Custom hook for easy theme access
export const usePortalTheme = (promoterId: string) => {
  const { data: portalData, isLoading, error } = usePortalSettings(promoterId);

  console.log('usePortalTheme - portalData:', portalData);

  // The API response structure is: { success: true, data: { resolvedSettings: {...} } }
  const resolvedSettings = portalData?.data?.resolvedSettings;

  console.log('usePortalTheme - resolvedSettings:', resolvedSettings);

  return {
    resolvedSettings,
    isLoading,
    error,
    theme: resolvedSettings?.global?.theme || 'light',
    accentColor: resolvedSettings?.global?.accentColor || '#3b82f6',
    borderWidth: resolvedSettings?.global?.borderWidth || 1,
    cornerStyle: resolvedSettings?.global?.panelSettings?.cornerStyle || 'rounded',
    fontFamily: resolvedSettings?.global?.typography?.fontFamily || 'Inter, system-ui, sans-serif',
    backgroundColor: resolvedSettings?.global?.backgroundColor || { color: '#ffffff', opacity: 1 },
    linkPageSettings: resolvedSettings?.linkPage,
    eventPageSettings: resolvedSettings?.eventPage,
  };
};
