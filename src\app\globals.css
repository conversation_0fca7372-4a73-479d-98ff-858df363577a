@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Portal Settings CSS Variables - Default values */
  --portal-theme: 'light';
  --portal-accent-color: #3b82f6;
  --portal-border-width: 1px;
  --portal-background-color: #ffffff;
  --portal-background-opacity: 1;
  --portal-font-family: 'Inter, system-ui, sans-serif';
  --portal-corner-radius: 0.375rem;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-display: var(--font-poppins);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), system-ui, sans-serif;
}

/* Custom utility classes */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom border width for 3px */
.border-3 {
  border-width: 3px;
}

/* Portal Settings Utility Classes */
.portal-panel {
  background-color: var(--portal-background-color); /* Panel background */
  border: var(--portal-border-width) solid var(--portal-accent-color); /* Panel border with accent color */
  border-radius: var(--portal-corner-radius); /* Panel border radius (rounded: 0.375rem, square: 0) */
  opacity: var(--portal-background-opacity);
}

.portal-typography {
  font-family: var(--portal-font-family); /* Typography for whole page */
}

.portal-button {
  background-color: var(--portal-accent-color);
  border-radius: var(--portal-corner-radius);
  border: var(--portal-border-width) solid transparent;
  font-family: var(--portal-font-family);
}

.portal-input:focus {
  border-color: var(--portal-accent-color);
  border-width: var(--portal-border-width);
}

/* Theme-specific styles */
.theme-light {
  color: #000000;
}

.theme-dark {
  color: #ffffff;
}

.corner-rounded {
  border-radius: 0.375rem;
}

.corner-square {
  border-radius: 0;
}

/* Focus styles for accessibility */
.focus-visible:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
