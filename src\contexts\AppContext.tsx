'use client';

import { createContext, useContext, useReducer, ReactNode } from 'react';

interface AppState {
  searchFilters: {
    category?: string;
    city?: string;
    dateRange?: { from: string; to: string };
  };
  cart: {
    eventId?: string;
    categoryId?: string;
    quantity: number;
  } | null;
}

type AppAction =
  | { type: 'SET_SEARCH_FILTERS'; payload: Partial<AppState['searchFilters']> }
  | { type: 'SET_CART'; payload: AppState['cart'] }
  | { type: 'CLEAR_CART' };

const initialState: AppState = {
  searchFilters: {},
  cart: null
};

const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);

function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_SEARCH_FILTERS':
      return {
        ...state,
        searchFilters: { ...state.searchFilters, ...action.payload }
      };
    case 'SET_CART':
      return {
        ...state,
        cart: action.payload
      };
    case 'CLEAR_CART':
      return {
        ...state,
        cart: null
      };
    default:
      return state;
  }
}

export function AppProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
}

export function useAppContext() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within AppProvider');
  }
  return context;
}
