'use client';

import { format, parseISO, isSameDay } from 'date-fns';

interface DateContainerProps {
  dateTime: string;
  endDateTime?: string;
  className?: string;
}

export function DateContainer({ dateTime, endDateTime, className = '' }: DateContainerProps) {
  const startDate = parseISO(dateTime);
  const endDate = endDateTime ? parseISO(endDateTime) : null;
  
  // Check if it's a single date or date range
  const isSingleDate = !endDate || isSameDay(startDate, endDate);
  
  if (isSingleDate) {
    // Single date - collapsed to square element
    return (
      <div className={`flex-shrink-0 w-10 h-10 min-[48rem]:w-12 min-[48rem]:h-12 bg-black border border-gray-900 flex flex-col items-center justify-center text-white ${className}`}>
        <div className="text-[10px] font-medium uppercase tracking-wider">
          {format(startDate, 'MMM')}
        </div>
        <div className="text-sm min-[48rem]:text-base font-bold leading-none">
          {format(startDate, 'd')}
        </div>
      </div>
    );
  }
  
  // Date range - vertical rectangular container
  return (
    <div className={`flex-shrink-0 w-10 h-16 min-[48rem]:w-12 min-[48rem]:h-18 bg-black border border-gray-900 flex flex-col text-white overflow-hidden ${className}`}>
      {/* Top half - Start date */}
      <div className="flex-1 flex flex-col items-center justify-center border-b border-white/30">
        <div className="text-[10px] font-medium uppercase leading-none tracking-wider">
          {format(startDate, 'MMM')}
        </div>
        <div className="text-xs font-bold leading-none">
          {format(startDate, 'd')}
        </div>
      </div>

      {/* Bottom half - End date */}
      <div className="flex-1 flex flex-col items-center justify-center">
        <div className="text-[10px] font-medium uppercase leading-none tracking-wider">
          {format(endDate, 'MMM')}
        </div>
        <div className="text-xs font-bold leading-none">
          {format(endDate, 'd')}
        </div>
      </div>
    </div>
  );
}
