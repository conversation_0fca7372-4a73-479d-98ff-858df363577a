import { useQuery } from '@tanstack/react-query';
import { 
  getPublicEvents, 
  getPublicEvent, 
  getPromoterPublicEvents,
  getAvailableCategories 
} from '@/services/eventService';
import { EventFilters } from '@/types';

export const useEvents = (params: EventFilters = {}) => {
  return useQuery({
    queryKey: ['events', params],
    queryFn: () => getPublicEvents(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2, // Limit retries to prevent excessive requests
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnMount: false, // Prevent refetch on component mount if data exists
  });
};

export const useEvent = (eventId: string) => {
  return useQuery({
    queryKey: ['event', eventId],
    queryFn: () => getPublicEvent(eventId),
    enabled: !!eventId,
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });
};

export const usePromoterEvents = (promoterId: string, params: EventFilters = {}) => {
  return useQuery({
    queryKey: ['promoter-events', promoterId, params],
    queryFn: () => getPromoterPublicEvents(promoterId, params),
    enabled: !!promoterId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });
};

export const useEventCategories = (eventId: string) => {
  return useQuery({
    queryKey: ['event-categories', eventId],
    queryFn: () => getAvailableCategories(eventId),
    enabled: !!eventId,
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};
