# Universal FTP Deployment System

A smart, reusable FTP deployment script that automatically detects your project type and deploys accordingly.

## 🚀 Quick Setup for Any Project

### Method 1: Copy Files Manually
1. Copy these files to your new project:
   - `deploy-ftp-universal.js`
   - `.env.ftp` (update with your credentials)
   - `.npmrc`

2. Add to your `package.json`:
```json
{
  "scripts": {
    "deploy:ftp": "node deploy-ftp-universal.js",
    "deploy:ftp:full": "npm run build && npm run deploy:ftp"
  },
  "dependencies": {
    "basic-ftp": "^5.0.5",
    "dotenv": "^16.0.0"
  }
}
```

### Method 2: Use Setup Script
1. Copy `setup-ftp-deployment.js` to your new project
2. Run: `node setup-ftp-deployment.js`
3. Edit `.env.ftp` with your credentials
4. Run: `npm install`

## 🎯 Supported Project Types

The script automatically detects and handles:

### Next.js Projects
- **Detects**: `next` in dependencies
- **Builds**: Runs `npm run build`
- **Uploads**: `.next`, `src`, `pages`, `components`, `public`, etc.
- **Files**: `package.json`, `server.js`, `.npmrc`

### React Projects (CRA, Vite)
- **Detects**: `react` in dependencies
- **Builds**: Runs `npm run build`
- **Uploads**: `build` or `dist` folder, `src`, `public`
- **Files**: `package.json`, `.npmrc`

### Vue.js Projects
- **Detects**: `vue` in dependencies
- **Builds**: Runs `npm run build`
- **Uploads**: `dist` folder, `src`, `public`
- **Files**: `package.json`, `.npmrc`

### Node.js Projects
- **Detects**: No frontend framework, has `package.json`
- **Builds**: No build step
- **Uploads**: `src`, `lib`, `routes`, `controllers`, etc.
- **Files**: `package.json`, `server.js`, `index.js`, `app.js`

### Static HTML Projects
- **Detects**: `index.html` exists, no `package.json`
- **Builds**: No build step
- **Uploads**: `css`, `js`, `images`, `assets`
- **Files**: `index.html`

## 📁 Configuration

### .env.ftp
```env
FTP_HOST=ftp.yourhost.com
FTP_USERNAME=your-username
FTP_PASSWORD=your-password
FTP_REMOTE_DIR=/public_html/your-app
```

### .npmrc (Optional)
```
production=true
```
Forces production-only npm installs by default.

## 🛠️ Usage

### Deploy with Build
```bash
npm run deploy:ftp:full
```

### Deploy Only (Skip Build)
```bash
npm run deploy:ftp
```

### Direct Script
```bash
node deploy-ftp-universal.js
```

## 🔧 Customization

You can modify the project detection logic in `deploy-ftp-universal.js`:

```javascript
// Add custom project type
getCustomConfig() {
    console.log('🔍 Detected: Custom project');
    return {
        type: 'custom',
        buildCommand: 'npm run custom-build',
        essentialFiles: ['package.json', 'custom.config.js'],
        buildDirs: ['output'],
        sourceDirs: ['source', 'assets'],
        excludePatterns: ['node_modules', '.git', '*.log']
    };
}
```

## 📋 What Gets Excluded

The script automatically excludes:
- `node_modules/` (too large)
- `.git/` (version control)
- `.env.*` files (sensitive data)
- `deploy-ftp*.js` (deployment scripts)
- `README.md` and `*.md` files
- Log files and temporary files

## 🔍 Troubleshooting

### Connection Issues
- Verify FTP credentials in `.env.ftp`
- Check if FTP server allows connections
- Try passive mode if needed

### Build Failures
- Ensure all dependencies are installed
- Fix any TypeScript/linting errors
- Check build scripts in `package.json`

### Upload Failures
- Verify remote directory permissions
- Check available disk space
- Ensure remote directory exists

## 🎉 Benefits

- **Smart Detection**: Automatically configures for your project type
- **Universal**: Works with Next.js, React, Vue, Node.js, and static sites
- **Reusable**: Copy once, use everywhere
- **Robust**: Handles errors gracefully
- **Fast**: Only uploads necessary files
- **Safe**: Excludes sensitive and unnecessary files

## 📝 Post-Deployment

### For Next.js/Node.js:
```bash
cd /your/remote/directory
npm install
npm start
```

### For React/Vue:
Configure your web server to serve the `build/` or `dist/` folder.

### For Static Sites:
Files are ready to serve immediately!
