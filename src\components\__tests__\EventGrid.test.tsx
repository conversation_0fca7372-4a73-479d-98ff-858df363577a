import { render, screen } from '@testing-library/react';
import { EventGrid } from '../EventGrid';
import { Event } from '@/types';

const mockEvents: Event[] = [
  {
    _id: '1',
    title: 'Test Event 1',
    description: 'A test event description',
    dateTime: '2024-12-25T19:00:00Z',
    location: {
      venue: 'Test Venue',
      address: '123 Test St',
      city: 'Test City',
      coordinates: [0, 0],
    },
    promoterId: {
      _id: 'promoter1',
      name: 'Test Promoter',
    },
    priceCategories: [
      {
        _id: 'cat1',
        name: 'General Admission',
        price: 50,
        totalTickets: 100,
        soldTickets: 20,
        availableTickets: 80,
      },
    ],
    category: 'music',
    tags: ['rock', 'live'],
    salesActive: true,
    availableTickets: 80,
  },
  {
    _id: '2',
    title: 'Test Event 2',
    description: 'Another test event',
    dateTime: '2024-12-26T20:00:00Z',
    location: {
      venue: 'Another Venue',
      address: '456 Test Ave',
      city: 'Test City',
      coordinates: [1, 1],
    },
    promoterId: {
      _id: 'promoter2',
      name: 'Another Promoter',
    },
    priceCategories: [],
    category: 'sports',
    tags: [],
    salesActive: true,
    availableTickets: 0,
  },
];

describe('EventGrid Component', () => {
  it('renders events correctly', () => {
    render(<EventGrid events={mockEvents} />);
    
    expect(screen.getByText('Test Event 1')).toBeInTheDocument();
    expect(screen.getByText('Test Event 2')).toBeInTheDocument();
    expect(screen.getByText('Test Venue')).toBeInTheDocument();
    expect(screen.getByText('Another Venue')).toBeInTheDocument();
  });

  it('shows loading state', () => {
    render(<EventGrid events={[]} loading={true} />);
    
    expect(screen.getByText('Loading events...')).toBeInTheDocument();
  });

  it('shows error state', () => {
    const error = new Error('Failed to load');
    render(<EventGrid events={[]} error={error} />);
    
    expect(screen.getByText('Failed to load events. Please try again later.')).toBeInTheDocument();
  });

  it('shows empty state with custom message', () => {
    const emptyMessage = 'No events available';
    render(<EventGrid events={[]} emptyMessage={emptyMessage} />);
    
    expect(screen.getByText(emptyMessage)).toBeInTheDocument();
  });

  it('displays event prices correctly', () => {
    render(<EventGrid events={mockEvents} />);
    
    expect(screen.getByText('$50')).toBeInTheDocument();
    expect(screen.getByText('Price TBA')).toBeInTheDocument();
  });

  it('shows availability status', () => {
    render(<EventGrid events={mockEvents} />);
    
    expect(screen.getByText('Available')).toBeInTheDocument();
    expect(screen.getByText('Sold Out')).toBeInTheDocument();
  });

  it('displays event categories', () => {
    render(<EventGrid events={mockEvents} />);
    
    expect(screen.getByText('music')).toBeInTheDocument();
    expect(screen.getByText('sports')).toBeInTheDocument();
  });

  it('formats dates correctly', () => {
    render(<EventGrid events={mockEvents} />);
    
    expect(screen.getByText('Dec 25, 2024')).toBeInTheDocument();
    expect(screen.getByText('Dec 26, 2024')).toBeInTheDocument();
  });
});
